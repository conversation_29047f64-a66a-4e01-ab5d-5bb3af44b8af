/**
 * 测试往返程航班时间格式修复功能
 * 验证 deptTime 和 arrTime 字段是否正确包含日期信息
 */

// 模拟测试数据
const mockOrderForm = {
  isRoundTrip: true,
  departure: {
    flightNo: 'CZ3101',
    deptTime: '09:15',  // 只包含时间
    arrTime: '11:30',   // 只包含时间
    deptDate: '2025-08-01',  // 日期信息
    deptAirport: 'PEK',
    arrAirport: 'CAN',
    flightBaseInfoVo: {
      deptTime: '09:15',  // 嵌套对象中也只包含时间
      arrTime: '11:30',   // 嵌套对象中也只包含时间
      flightNo: 'CZ3101'
    }
  },
  return: {
    flightNo: 'CZ3102',
    deptTime: '14:20',  // 只包含时间
    arrTime: '16:45',   // 只包含时间
    deptDate: '2025-08-03',  // 日期信息
    deptAirport: 'CAN',
    arrAirport: 'PEK',
    flightBaseInfoVo: {
      deptTime: '14:20',  // 嵌套对象中也只包含时间
      arrTime: '16:45',   // 嵌套对象中也只包含时间
      flightNo: 'CZ3102'
    }
  }
}

// 模拟修复逻辑
function fixTimeFormat(orderForm) {
  const result = { ...orderForm }
  
  if (orderForm.isRoundTrip) {
    // 处理去程航班时间格式
    const departure = { ...orderForm.departure }
    if (departure.deptTime && departure.deptDate) {
      if (departure.deptTime.length <= 5) {
        departure.deptTime = `${departure.deptDate} ${departure.deptTime}:00`
      }
    }
    if (departure.arrTime && departure.deptDate) {
      if (departure.arrTime.length <= 5) {
        departure.arrTime = `${departure.deptDate} ${departure.arrTime}:00`
      }
    }

    // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
    if (departure.flightBaseInfoVo) {
      if (departure.flightBaseInfoVo.deptTime && departure.deptDate) {
        if (departure.flightBaseInfoVo.deptTime.length <= 5) {
          departure.flightBaseInfoVo.deptTime = `${departure.deptDate} ${departure.flightBaseInfoVo.deptTime}:00`
        }
      }
      if (departure.flightBaseInfoVo.arrTime && departure.deptDate) {
        if (departure.flightBaseInfoVo.arrTime.length <= 5) {
          departure.flightBaseInfoVo.arrTime = `${departure.deptDate} ${departure.flightBaseInfoVo.arrTime}:00`
        }
      }
    }

    // 处理回程航班时间格式
    const returnFlight = { ...orderForm.return }
    if (returnFlight.deptTime && returnFlight.deptDate) {
      if (returnFlight.deptTime.length <= 5) {
        returnFlight.deptTime = `${returnFlight.deptDate} ${returnFlight.deptTime}:00`
      }
    }
    if (returnFlight.arrTime && returnFlight.deptDate) {
      if (returnFlight.arrTime.length <= 5) {
        returnFlight.arrTime = `${returnFlight.deptDate} ${returnFlight.arrTime}:00`
      }
    }

    // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
    if (returnFlight.flightBaseInfoVo) {
      if (returnFlight.flightBaseInfoVo.deptTime && returnFlight.deptDate) {
        if (returnFlight.flightBaseInfoVo.deptTime.length <= 5) {
          returnFlight.flightBaseInfoVo.deptTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.deptTime}:00`
        }
      }
      if (returnFlight.flightBaseInfoVo.arrTime && returnFlight.deptDate) {
        if (returnFlight.flightBaseInfoVo.arrTime.length <= 5) {
          returnFlight.flightBaseInfoVo.arrTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.arrTime}:00`
        }
      }
    }
    
    result.departure = departure
    result.return = returnFlight
  }
  
  return result
}

// 执行测试
console.log('=== 往返程航班时间格式修复测试 ===')
console.log('修复前的数据:')
console.log('去程 deptTime:', mockOrderForm.departure.deptTime)
console.log('去程 arrTime:', mockOrderForm.departure.arrTime)
console.log('去程 flightBaseInfoVo.deptTime:', mockOrderForm.departure.flightBaseInfoVo.deptTime)
console.log('去程 flightBaseInfoVo.arrTime:', mockOrderForm.departure.flightBaseInfoVo.arrTime)
console.log('回程 deptTime:', mockOrderForm.return.deptTime)
console.log('回程 arrTime:', mockOrderForm.return.arrTime)
console.log('回程 flightBaseInfoVo.deptTime:', mockOrderForm.return.flightBaseInfoVo.deptTime)
console.log('回程 flightBaseInfoVo.arrTime:', mockOrderForm.return.flightBaseInfoVo.arrTime)

const fixedData = fixTimeFormat(mockOrderForm)

console.log('\n修复后的数据:')
console.log('去程 deptTime:', fixedData.departure.deptTime)
console.log('去程 arrTime:', fixedData.departure.arrTime)
console.log('去程 flightBaseInfoVo.deptTime:', fixedData.departure.flightBaseInfoVo.deptTime)
console.log('去程 flightBaseInfoVo.arrTime:', fixedData.departure.flightBaseInfoVo.arrTime)
console.log('回程 deptTime:', fixedData.return.deptTime)
console.log('回程 arrTime:', fixedData.return.arrTime)
console.log('回程 flightBaseInfoVo.deptTime:', fixedData.return.flightBaseInfoVo.deptTime)
console.log('回程 flightBaseInfoVo.arrTime:', fixedData.return.flightBaseInfoVo.arrTime)

// 验证结果
const tests = [
  {
    name: '去程出发时间格式',
    expected: '2025-08-01 09:15:00',
    actual: fixedData.departure.deptTime,
    pass: fixedData.departure.deptTime === '2025-08-01 09:15:00'
  },
  {
    name: '去程到达时间格式',
    expected: '2025-08-01 11:30:00',
    actual: fixedData.departure.arrTime,
    pass: fixedData.departure.arrTime === '2025-08-01 11:30:00'
  },
  {
    name: '回程出发时间格式',
    expected: '2025-08-03 14:20:00',
    actual: fixedData.return.deptTime,
    pass: fixedData.return.deptTime === '2025-08-03 14:20:00'
  },
  {
    name: '回程到达时间格式',
    expected: '2025-08-03 16:45:00',
    actual: fixedData.return.arrTime,
    pass: fixedData.return.arrTime === '2025-08-03 16:45:00'
  },
  {
    name: '去程嵌套对象出发时间格式',
    expected: '2025-08-01 09:15:00',
    actual: fixedData.departure.flightBaseInfoVo.deptTime,
    pass: fixedData.departure.flightBaseInfoVo.deptTime === '2025-08-01 09:15:00'
  },
  {
    name: '去程嵌套对象到达时间格式',
    expected: '2025-08-01 11:30:00',
    actual: fixedData.departure.flightBaseInfoVo.arrTime,
    pass: fixedData.departure.flightBaseInfoVo.arrTime === '2025-08-01 11:30:00'
  },
  {
    name: '回程嵌套对象出发时间格式',
    expected: '2025-08-03 14:20:00',
    actual: fixedData.return.flightBaseInfoVo.deptTime,
    pass: fixedData.return.flightBaseInfoVo.deptTime === '2025-08-03 14:20:00'
  },
  {
    name: '回程嵌套对象到达时间格式',
    expected: '2025-08-03 16:45:00',
    actual: fixedData.return.flightBaseInfoVo.arrTime,
    pass: fixedData.return.flightBaseInfoVo.arrTime === '2025-08-03 16:45:00'
  }
]

console.log('\n=== 测试结果 ===')
tests.forEach(test => {
  const status = test.pass ? '✅ PASS' : '❌ FAIL'
  console.log(`${status} ${test.name}`)
  console.log(`   期望: ${test.expected}`)
  console.log(`   实际: ${test.actual}`)
})

const allPassed = tests.every(test => test.pass)
console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 存在测试失败'}`)

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { fixTimeFormat, tests }
}

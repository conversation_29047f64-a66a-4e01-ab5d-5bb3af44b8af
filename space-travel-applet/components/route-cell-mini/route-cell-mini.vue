<script>
export default {
  name: 'RouteCellMini',
  props: {
    cellData: {
      type: Object,
      default: () => ({}),
    },
    flightType: {
      type: String,
      default: 'departure', // departure | return
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 计算最低价格（仅计算经济舱）
    minPrice() {
      if (!this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        console.log('⚠️ route-cell-mini 价格计算: 没有舱位数据', this.cellData)
        return 0
      }

      // 添加详细调试信息
      console.log('🔍 route-cell-mini 价格计算调试:')
      console.log('  - cellData:', this.cellData)
      console.log('  - cabinClses数量:', this.cellData.cabinClses.length)
      console.log('  - 第一个舱位数据:', this.cellData.cabinClses[0])

      // 先过滤出经济舱舱位
      const economyCabins = this.cellData.cabinClses.filter((cabin) => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toUpperCase()
        // 经济舱代码（与舱位选择页面保持一致）
        return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
      })

      console.log(`  - 经济舱舱位数量: ${economyCabins.length}`)

      if (economyCabins.length === 0) {
        console.log('  - 没有经济舱舱位，返回0')
        return 0
      }

      const prices = economyCabins.map((cabin, index) => {
        // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
        const basePrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
        const taxFee = parseFloat(cabin.taxFeeAdult || 0)
        // 只计算票价，不包含机建燃油费
        const totalPrice = basePrice

        console.log(`  - 经济舱舱位${index + 1} (${cabin.cabinCode || cabin.cabinCls || cabin.cabin}):`)
        console.log('    * 原始数据:', cabin)
        console.log(`    * priceAdult: ${cabin.priceAdult}`)
        console.log(`    * adultOrigPrice: ${cabin.adultOrigPrice}`)
        console.log(`    * 选择的basePrice: ${basePrice} (优先级: priceAdult → adultOrigPrice)`)
        console.log(`    * taxFee(taxFeeAdult): ${cabin.taxFeeAdult} → ${taxFee}`)
        console.log(`    * 总价(票价+): ${totalPrice}`)

        return totalPrice
      }).filter(price => price > 0)

      const minPrice = prices.length > 0 ? Math.min(...prices) : 0
      console.log(`  - 最终经济舱最低价格(票价): ${minPrice}`)

      return minPrice
    },
  },
  methods: {
    // 格式化时间显示
    formatTime(timeStr) {
      if (!timeStr)
        return ''

      try {
        // 处理iOS兼容性问题：将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy-MM-ddTHH:mm:ss"
        let isoTimeStr = timeStr
        if (typeof timeStr === 'string' && timeStr.includes(' ') && timeStr.includes('-')) {
          // 将空格替换为T，确保iOS兼容性
          isoTimeStr = timeStr.replace(' ', 'T')
        }

        const date = new Date(isoTimeStr)

        // 检查日期是否有效
        if (isNaN(date.getTime()))
          throw new Error('Invalid date')

        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      }
      catch (e) {
        console.warn('日期解析失败:', timeStr, e)

        // 如果解析失败，尝试直接提取时间部分
        if (typeof timeStr === 'string') {
          // 尝试匹配 HH:mm 格式
          const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})/)
          if (timeMatch) {
            const hours = timeMatch[1].padStart(2, '0')
            const minutes = timeMatch[2]
            return `${hours}:${minutes}`
          }

          // 如果包含空格，尝试获取时间部分
          if (timeStr.includes(' ')) {
            const timePart = timeStr.split(' ')[1]
            if (timePart && timePart.includes(':'))
              return timePart.substring(0, 5)
          }
        }

        // 最后的兜底处理
        return timeStr || ''
      }
    },

    // 获取舱位显示信息（舱位名称）
    getCabinDisplayInfo(cabin) {
      // 增强数据验证
      if (!cabin || typeof cabin !== 'object')
        return '经济舱'

      // 安全获取舱位名称
      const cabinName = cabin.cabinName || cabin.cabinClsName || ''
      const cabinCode = cabin.cabinCode || cabin.cabinCls || cabin.cabin || ''

      // 如果有舱位名称，直接返回
      if (cabinName && cabinName !== '')
        return cabinName

      // 如果没有舱位名称，根据舱位代码推断
      if (cabinCode && typeof cabinCode === 'string') {
        const code = cabinCode.toUpperCase()

        // 头等舱代码
        if (['F', 'A'].includes(code))
          return '头等舱'

        // 公务舱代码
        if (['C', 'J', 'D', 'I', 'Z', 'P', 'O'].includes(code))
          return '公务舱'

        // 超级经济舱代码（部分航司使用）
        if (['W', 'S'].includes(code))
          return '超级经济舱'

        // 经济舱代码
        if (['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'N', 'R', 'G', 'X', 'B'].includes(code))
          return '经济舱'
      }

      // 默认返回经济舱
      return '经济舱'
    },
  },
}
</script>

<template>
  <view class="route-cell-mini" :class="{ selected: isSelected }">
    <!-- 航班基本信息 -->
    <view class="flight-header">
      <view class="airline-info">
        <image class="airline-logo" :src="cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.airIconUrl" mode="aspectFit" />
        <text class="airline-name">
          {{ cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.airName }}
        </text>
        <text class="flight-number">
          {{ cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.flightNo }}
        </text>
      </view>
      <view class="aircraft-type">
        <text>{{ cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.planeType }}</text>
      </view>
    </view>

    <!-- 时间和机场信息 -->
    <view class="flight-time">
      <view class="departure">
        <text class="time">
          {{ formatTime(cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.deptTime) }}
        </text>
        <text class="airport">
          {{ cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.deptAirportName }}
        </text>
        <text v-if="cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.depTerminal" class="terminal">
          {{ cellData.flightBaseInfoVo.depTerminal }}
        </text>
      </view>

      <view class="duration">
        <view class="flight-path">
          <view class="dot start" />
          <view class="line" :class="{ direct: !(cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.stop) }" />
          <view class="dot end" />
        </view>
        <text v-if="cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.stop && !['N', 'A', 'Y', 'F', 'C', 'J', 'D', 'I', 'Z', 'P', 'O'].includes(cellData.flightBaseInfoVo.stop)" class="duration-text">
          {{ cellData.flightBaseInfoVo.stop || '直飞' }}
        </text>
      </view>

      <view class="arrival">
        <text class="time">
          {{ formatTime(cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.arrTime) }}
        </text>
        <text class="airport">
          {{ cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.arrAirportName }}
        </text>
        <text v-if="cellData.flightBaseInfoVo && cellData.flightBaseInfoVo.arrTerminal" class="terminal">
          {{ cellData.flightBaseInfoVo.arrTerminal }}
        </text>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="flight-price">
      <!-- 隐藏舱位信息显示 -->
      <!-- <view class="cabin-info" v-if="cellData.cabinClses && cellData.cabinClses.length">
        <text class="cabin-class">{{ getCabinDisplayInfo(cellData.cabinClses[0]) }}</text>
        <text class="remaining" v-if="cellData.cabinClses[0] && cellData.cabinClses[0].remaining">剩{{ cellData.cabinClses[0].remaining }}张</text>
      </view> -->
      <view class="price-info">
        <text class="price-label">
          起
        </text>
        <text class="price-amount">
          ¥{{ minPrice }}
        </text>
      </view>
    </view>

    <!-- 选中标识 -->
    <view v-if="isSelected" class="selected-indicator">
      <uni-icons custom-prefix="iconfont" type="icon-xuanzhong" size="20" color="#faad14" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.route-cell-mini {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;

  &.selected {
    border-color: #faad14;
    box-shadow: 0 2px 8px rgba(250, 173, 20, 0.25);
    background: #fffbe6;
  }

  &:active {
    transform: scale(0.98);
  }
}

.flight-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .airline-info {
    display: flex;
    align-items: center;
    flex: 1;

    .airline-logo {
      width: 20px;
      height: 20px;
      margin-right: 6px;
      border-radius: 4px;
    }

    .airline-name {
      font-size: 12px;
      color: #666;
      margin-right: 6px;
    }

    .flight-number {
      font-size: 13px;
      font-weight: 500;
      color: #333;
    }
  }

  .aircraft-type {
    font-size: 11px;
    color: #999;
  }
}

.flight-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;

  .departure, .arrival {
    flex: 1;
    text-align: center;

    .time {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }

    .airport {
      display: block;
      font-size: 11px;
      color: #666;
      margin-top: 2px;
      line-height: 1.2;
    }

    .terminal {
      display: block;
      font-size: 10px;
      color: #999;
      margin-top: 1px;
    }
  }

  .departure {
    text-align: left;
  }

  .arrival {
    text-align: right;
  }

  .duration {
    flex: 0 0 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px;

    .flight-path {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 4px;

      .dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #007bff;

        &.start {
          margin-right: 2px;
        }

        &.end {
          margin-left: 2px;
        }
      }

      .line {
        flex: 1;
        height: 1px;
        background: #ddd;
        position: relative;

        &.direct {
          background: #007bff;
        }

        &:not(.direct)::after {
          content: '';
          position: absolute;
          top: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 4px;
          height: 4px;
          background: #ffa500;
          border-radius: 50%;
        }
      }
    }

    .duration-text {
      font-size: 10px;
      color: #999;
      text-align: center;
    }
  }
}

.flight-price {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .cabin-info {
    flex: 1;

    .cabin-class {
      font-size: 11px;
      color: #666;
      margin-right: 6px;
    }

    .remaining {
      font-size: 10px;
      color: #ffa500;
    }
  }

  .price-info {
    display: flex;
    align-items: baseline;

    .price-label {
      font-size: 10px;
      color: #999;
      margin-right: 2px;
    }

    .price-amount {
      font-size: 16px;
      font-weight: 600;
      color: #ff4757;
    }
  }
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 123, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

// 隐藏舱位代码标识
.cabin-code,
.cabin-letter,
.cabin-type,
[class*="cabin-code"],
[class*="cabin-letter"],
[class*="cabin-type"] {
  display: none !important;
}

// 隐藏可能包含单个字母的元素
text[class*="cabin"]:empty::before,
view[class*="cabin"]:empty::after {
  display: none !important;
}

// 隐藏可能的舱位代码相关元素
.flight-time .cabin-indicator,
.duration .cabin-indicator,
.price-info .cabin-indicator {
  display: none !important;
}

// 响应式适配
@media (max-width: 400px) {
  .route-cell-mini {
    padding: 10px;

    .flight-time {
      .departure, .arrival {
        .time {
          font-size: 14px;
        }

        .airport {
          font-size: 10px;
        }
      }

      .duration {
        flex: 0 0 50px;
        margin: 0 8px;
      }
    }

    .flight-price {
      .price-info {
        .price-amount {
          font-size: 14px;
        }
      }
    }
  }
}
</style>

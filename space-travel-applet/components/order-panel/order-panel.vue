<script>
export default {
  name: 'OrderPanel',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    showOtherInfo: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    flightPrice: {
      type: [Number, String],
      default: null,
    },
    rowData: {
      type: Object,
      default: () => {
        return {
          airCode: '',
          arrAirport: '',
          arrAirportName: '',
          arrCityName: '',
          arrTerminal: '',
          arrTime: '',
          cabinCls: '',
          cabinClsName: '',
          depTerminal: '',
          deptAirport: '',
          deptAirportName: '',
          deptCityName: '',
          deptDate: '',
          deptTime: '',
          flightNo: '',
          meal: '',
          planeType: '',
          priceAdult: '',
          adultOrigPrice: '',
          productCode: '',
          stop: '',
          taxFeeAdult: '',
        }
      },
      // 确保传入的数据不是null
      validator(value) {
        return value !== null && value !== undefined
      },
    },
  },
  data() {
    return {
      show: false,
    }
  },
  computed: {
    // 确保使用安全的rowData，防止null导致的渲染错误
    safeRowData() {
      const result = (this.rowData && typeof this.rowData === 'object')
        ? this.rowData
        : {
            airCode: '',
            arrAirport: '',
            arrAirportName: '',
            arrCityName: '',
            arrTerminal: '',
            arrTime: '',
            cabinCls: '',
            cabinClsName: '',
            depTerminal: '',
            deptAirport: '',
            deptAirportName: '',
            deptCityName: '',
            deptDate: '',
            deptTime: '',
            flightNo: '',
            meal: '',
            planeType: '',
            priceAdult: '',
            adultOrigPrice: '',
            productCode: '',
            stop: '',
            taxFeeAdult: '',
          }

      // 只在有问题时输出调试信息
      if (!result.flightNo && this.rowData) {
        console.log('🔍 order-panel 调试 - 缺少flightNo:')
        console.log('   - rowData:', this.rowData)
        console.log('   - result:', result)
      }

      return result
    },
  },
  methods: {
    closeRulesPopup() {
      this.show = false
    },
    openRulesPopup() {
      this.show = true
    },
    parseRules(rules) {
      if (!rules)
        return ''
      const res = rules.replace(/\\n/g, '<br/>')
      return res
    },
  },
}
</script>

<template>
  <view class="order-panel-box">
    <view class="order-panel-wrap">
      <u-skeleton
        rows="3"
        :loading="loading"
        title-width="100%"
        title-height="24"
        :rows-height="['24', '40', '24']"
        :rows-width="['100%', '100%', '100%']"
      >
        <template v-if="safeRowData && safeRowData.flightNo">
          <view v-if="showHeader" class="cell-header">
            <view class="name">
              航班信息
            </view>
          </view>
          <view class="panel-title">
            <view class="panel-title-left">
              <text>{{ safeRowData && safeRowData.stop && (safeRowData.stop === '去程' || safeRowData.stop === '回程') ? safeRowData.stop : (safeRowData && safeRowData.stop ? '经停' : '直飞') }}</text>
              <text>{{ safeRowData && safeRowData.deptTime ? $formatDateStr(safeRowData.deptTime, 'yyyy年mm月dd日') : '' }}</text>
              <text>{{ safeRowData && safeRowData.deptTime ? $formatDateStr(safeRowData.deptTime, 'WW') : '' }}</text>
              <text>{{ `${safeRowData.deptCityName ? `${safeRowData.deptCityName}-` : ''}${safeRowData.arrCityName || ''}` }}</text>
            </view>
            <view v-if="flightPrice !== null && flightPrice !== undefined" class="panel-title-right">
              <text class="flight-price">
                ¥{{ flightPrice }}
              </text>
            </view>
          </view>
          <view class="step-row">
            <view class="title">
              <u-image :src="safeRowData && safeRowData.airIconUrl " :show-loading="true" width="16px" height="16px" />
              <text>{{ safeRowData && safeRowData.airName }} {{ safeRowData && safeRowData.flightNo }}</text>
              <view v-if="safeRowData && safeRowData.share" class="real-air">
                <i class="iconfont icon-down" style="font-size: 32rpx;color: #999999;margin-left: 8rpx;" />
                <text class="real-air-text">
                  实际承运
                </text>
                <u-image :src="rowData.operateIconUrl" :show-loading="true" width="16px" height="16px" />
                <text>{{ rowData.operateAirName }} {{ rowData.operateFlightNo }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="start">
                <view class="time">
                  {{ rowData && rowData.deptTime ? $formatDateStr(rowData.deptTime, 'hh:MM') : '' }}
                </view>
                <view class="name">
                  {{ rowData && rowData.deptAirportName }}{{ rowData && rowData.depTerminal }}
                </view>
              </view>
              <view class="through">
                <view class="through-time">
                  {{ (rowData && rowData.deptTime && rowData.arrTime) ? $timeDiff(rowData.deptTime, rowData.arrTime) : '' }}
                </view>
                <view class="line">
                  <view class="triangle" />
                </view>
                <view class="through-name">
                  {{ (rowData && rowData.stop) ? rowData.stop : '' }}
                </view>
              </view>
              <view class="end">
                <view class="time">
                  {{ (rowData && rowData.arrTime) ? $formatDateStr(rowData.arrTime, 'hh:MM') : '' }}
                </view>
                <view class="name">
                  {{ rowData && rowData.arrAirportName }}{{ rowData && rowData.arrTerminal }}
                </view>
              </view>
            </view>
            <view class="detail">
              <u-line v-if="showOtherInfo" color="#EFEFEF" />
              <view v-if="showOtherInfo" class="detail-name">
                <text>{{ rowData && rowData.cabinClsName }}</text>
                <text>成人票价</text>
                <text class="text-err">
                  ¥{{ (rowData && (rowData.priceAdult || rowData.adultOrigPrice)) ? $formatMoney(rowData.priceAdult || rowData.adultOrigPrice) : '' }}
                </text>
                <text>机建+燃油</text>
                <text class="text-err">
                  ¥{{ (rowData && rowData.taxFeeAdult) ? $formatMoney(rowData.taxFeeAdult) : '' }}
                </text>
                <view v-if="rowData && rowData.refundAndChangeRuleDes" class="text-info" @click="openRulesPopup">
                  <view class="text-info-right">
                    <u-icon name="info-circle" size="12" color="#999" />
                    退改政策
                  </view>
                </view>
              </view>
              <!-- <view class="detail-subname" :class="{ center: !showOtherInfo }">
                <text>支持退改签</text>
                <text>|</text>
                <text>托运行李30KG</text>
              </view> -->
            </view>
          </view>
        </template>
      </u-skeleton>
    </view>
    <u-popup class="rulesPop" :show="show" mode="bottom" :round="10" :close-on-click-overlay="true" @close="closeRulesPopup">
      <view class="rulesPop-title">
        <u-icon name="close" color="#000" size="16" @click="closeRulesPopup" />
        <text class="rulesPop-title-text">
          退改政策
        </text>
      </view>
      <view class="rulesPop-content">
        <rich-text :nodes="parseRules(rowData && rowData.refundAndChangeRuleDes)" />
      </view>
    </u-popup>
  </view>
</template>

<style lang="scss" scoped>
.order-panel-box{
  height: 180px;
  padding: 10px 16px 16px;
  background: #FFFFFF;
  box-shadow: 0px 2px 6px 0px rgba(202,202,202,0.42);
  border-radius: 8px;
  text-align: center;

  .cell-header{
    padding: 6px 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      width: 70px;
      height: 22px;
      font-size: 16px;
      font-weight: bold;
      line-height: 22px;
    }

    .add-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 120px;
      height: 24px;
      font-weight: bold;
      background: rgba(132,108,245,0.1);
      border-radius: 16px;
      color: $u-primary;
      text{
        margin-left: 4px;
      }
    }

    .cell-value{
      display: flex;
      align-items: center;
      width: calc(100% - 64px - 8px);

      text {
        margin-right: 7px;
        color: #666666;
      }
    }
  }

  .title{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    line-height: 17px;
    text {
      margin-left: 3px;
    }
    .real-air{
        &-text{
          margin-right: 8rpx;
        }
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
  }

  .panel-title{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 8px;
    overflow: hidden;

    .panel-title-left {
      display: flex;
      align-items: center;
      flex: 1;
      white-space: nowrap;

      text {
        margin-right: 8px;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
      }
      text:first-child {
        display: inline-block;
        height: 16px;
        padding: 0 8px;
        font-size: 10px;
        color: #FFFFFF;
        line-height: 16px;
        background: $u-warning;
        border-radius: 8px;
      }
    }

    .panel-title-right {
      flex-shrink: 0;

      .flight-price {
        font-size: 16px;
        font-weight: bold;
        color: #FF6B35;
        line-height: 24px;
      }
    }
  }

  .step-row{
    padding-top: 4px;

    .info-row{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 12px 0 16px;

      .start,
      .end{
        min-width: 64px;
        width: calc(50% - 30px);
      }

      .through{
        width: 64px;
        margin: 0 12px;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: center;

        .through-name{
          height: 14px;
          margin-top: 2px;
          font-size: 10px;
          line-height: 14px;
        }

        .line{
          position: relative;
          width: 100%;
          margin-top: 4px;
          border-bottom: 1px solid #D8D8D8;
          .triangle{
            position: absolute;
            right: -1px;
            bottom: -1px;
            height: 0px;
            width: 0px;
            border-style: solid;
            border-width:  6px 0 0 6px;
            border-color: transparent transparent transparent #D8D8D8;
          }
        }
      }

      .time{
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
      }
      .name{
        width: 100%;
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #666666;
      }
    }
    .detail{
      text-align: left;

      .detail-name{
        height: 16px;
        margin-top: 12px;
        margin-left: 10px;
        font-size: 12px;
        color: #666666;
        line-height: 16px;

        text {
          margin-right: 8px;
        }
      }

      .detail-subname{
        height: 16px;
        margin-left: 10px;
        margin-top: 8px;
        font-size: 12px;
        color: #999999;
        line-height: 16px;

        &.center{
          text-align: center;
          margin-left: 0px;
          margin-bottom: 2px;
        }

        text {
          margin-right: 4px;
        }
      }
    }
  }
  .detail{
    .text-info{
      display: inline-block;
      color: #999999;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      float: right;
      &-right{
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.rulesPop {
  position: relative;

  &-title {
    height: 88rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #323233;
    line-height: 44rpx;
    display: flex;
    justify-content: flex-start;
    padding-left: 32rpx;
    align-items: center;

    &-text {
      font-weight: bold;
      position: absolute;
      transform: translateX(-50%);
      left: 50%;
    }
  }

  &-content {
    min-height: 720rpx;
    max-height: 80vh;
    text-align: left;
    padding: 40rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
  }
}
</style>

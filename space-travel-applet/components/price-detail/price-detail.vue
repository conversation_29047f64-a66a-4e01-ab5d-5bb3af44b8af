<script>
export default {
  name: 'PriceDetail',
  props: {
    orderForm: {
      type: Object,
      default: () => ({}),
    },
    passengersAudlt: {
      type: Number,
      default: 0,
    },
    passengersChild: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    // 是否为往返程模式
    isRoundTrip() {
      return this.orderForm.isRoundTrip
    },
    // 获取当前航班数据（单程模式）
    currentFlight() {
      return this.orderForm.flight
    },
    // 获取去程数据
    departureFlight() {
      return this.orderForm.departure
    },
    // 获取回程数据
    returnFlight() {
      return this.orderForm.return
    },
    // 往返程成人总价
    roundTripAdultTotal() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight)
        return 0
      const departurePrice = Number(this.departureFlight.priceAdult || this.departureFlight.adultOrigPrice) + Number(this.departureFlight.taxFeeAdult)
      const returnPrice = Number(this.returnFlight.priceAdult || this.returnFlight.adultOrigPrice) + Number(this.returnFlight.taxFeeAdult)
      return departurePrice + returnPrice
    },
    // 往返程去程机票价格
    departureTicketPrice() {
      if (!this.departureFlight)
        return 0
      return Number(this.departureFlight.priceAdult || this.departureFlight.adultOrigPrice)
    },
    // 往返程回程机票价格
    returnTicketPrice() {
      if (!this.returnFlight)
        return 0
      return Number(this.returnFlight.priceAdult || this.returnFlight.adultOrigPrice)
    },
    // 往返程机场建设费总计（共2程）
    roundTripAirportFee() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight)
        return 0
      const departureAirportFee = Number(this.departureFlight.amtAdultAirPortFee) || 0
      const returnAirportFee = Number(this.returnFlight.amtAdultAirPortFee) || 0
      return departureAirportFee + returnAirportFee
    },
    // 往返程燃油附加费总计（共2程）
    roundTripOilFee() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight)
        return 0
      const departureOilFee = Number(this.departureFlight.oilFeeAdult) || 0
      const returnOilFee = Number(this.returnFlight.oilFeeAdult) || 0
      return departureOilFee + returnOilFee
    },
    // 往返程儿童总价
    roundTripChildTotal() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight || this.passengersChild === 0)
        return 0
      const departurePrice = Number(this.departureFlight.priceChild) + Number(this.departureFlight.taxFeeChild)
      const returnPrice = Number(this.returnFlight.priceChild) + Number(this.returnFlight.taxFeeChild)
      return departurePrice + returnPrice
    },
    // 往返程儿童机场建设费总计
    roundTripChildAirportFee() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight)
        return 0
      const departureAirportFee = Number(this.departureFlight.amtChildAirPortFee) || 0
      const returnAirportFee = Number(this.returnFlight.amtChildAirPortFee) || 0
      return departureAirportFee + returnAirportFee
    },
    // 往返程儿童燃油附加费总计
    roundTripChildOilFee() {
      if (!this.isRoundTrip || !this.departureFlight || !this.returnFlight)
        return 0
      const departureOilFee = Number(this.departureFlight.oilFeeChild) || 0
      const returnOilFee = Number(this.returnFlight.oilFeeChild) || 0
      return departureOilFee + returnOilFee
    },
  },
  methods: {
  },
}
</script>

<template>
  <view>
    <!-- 往返程模式 -->
    <template v-if="isRoundTrip">
      <!-- 成人价格明细 -->
      <view v-if="passengersAudlt > 0" class="order-popup-content">
        <view class="order-header">
          <text class="order-title">
            成人
          </text>
          <view class="order-money">
            <text>¥{{ $formatMoney(roundTripAdultTotal) || '--' }}</text>
            <text>×</text>
            <text>{{ passengersAudlt }}人</text>
          </view>
        </view>
        <view class="order-line" />
        <view class="order-content">
          <view class="order-header-small">
            <text class="order-title">
              成人去程机票
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(departureTicketPrice) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              成人返程机票
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(returnTicketPrice) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              机场建设费(共2程)
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(roundTripAirportFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              燃油附加费(共2程)
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(roundTripOilFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt }}人</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 儿童价格明细 -->
      <view v-if="passengersChild > 0" class="order-popup-content">
        <view class="order-header">
          <text class="order-title">
            儿童
          </text>
          <view class="order-money">
            <text>¥{{ $formatMoney(roundTripChildTotal) || '--' }}</text>
            <text>×</text>
            <text>{{ passengersChild }}人</text>
          </view>
        </view>
        <view class="order-line" />
        <view class="order-content">
          <view class="order-header-small">
            <text class="order-title">
              儿童去程机票
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(Number(departureFlight.priceChild)) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              儿童返程机票
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(Number(returnFlight.priceChild)) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              机场建设费(共2程)
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(roundTripChildAirportFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              燃油附加费(共2程)
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(roundTripChildOilFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild }}人</text>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- 单程模式（保持原有逻辑不变） -->
    <template v-else>
      <!-- 成人价格明细 -->
      <view v-if="currentFlight && passengersAudlt" class="order-popup-content">
        <view class="order-header">
          <text class="order-title">
            普通成人
          </text>
          <view class="order-money">
            <text>
              ¥{{ $formatMoney(Number(Number(currentFlight.priceAdult || currentFlight.adultOrigPrice)
                + Number(currentFlight.taxFeeAdult))) || '--' }}
            </text>
            <text>×</text>
            <text>{{ passengersAudlt || '--' }}人</text>
          </view>
        </view>
        <view class="order-line" />
        <view class="order-content">
          <view class="order-header-small">
            <text class="order-title">
              票价
            </text>
            <view class="order-money">
              <text>
                ¥{{ $formatMoney(Number(currentFlight.priceAdult || currentFlight.adultOrigPrice)) || '--' }}
              </text>
              <text>×</text>
              <text>{{ passengersAudlt || '--' }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              机建
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(currentFlight.amtAdultAirPortFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt || '--' }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              燃油
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(currentFlight.oilFeeAdult) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersAudlt || '--' }}人</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 儿童价格明细 -->
      <view v-if="passengersChild > 0 && currentFlight && currentFlight.priceChild" class="order-popup-content">
        <view class="order-header">
          <text class="order-title">
            儿童
          </text>
          <view class="order-money">
            <text>
              ¥{{ $formatMoney(Number(Number(currentFlight.priceChild)
                + Number(currentFlight.taxFeeChild))) || '--' }}
            </text>
            <text>×</text>
            <text>{{ passengersChild || '--' }}人</text>
          </view>
        </view>
        <view class="order-line" />
        <view class="order-content">
          <view class="order-header-small">
            <text class="order-title">
              票价
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(currentFlight.priceChild) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild || '--' }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              机建
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(currentFlight.amtChildAirPortFee) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild || '--' }}人</text>
            </view>
          </view>
          <view class="order-header-small">
            <text class="order-title">
              燃油
            </text>
            <view class="order-money">
              <text>¥{{ $formatMoney(currentFlight.oilFeeChild) || '--' }}</text>
              <text>×</text>
              <text>{{ passengersChild || '--' }}人</text>
            </view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.order-popup-content {
  width: calc(100% - 32rpx);
  margin: 0 auto;
  background: #fff;
  padding: 32rpx 32rpx 16rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    color: #333;

    .order-money {
      text {
        margin-right: 4px;
        color: #333;
      }
    }

    &.large {
      font-size: 16px;
    }
  }
  .order-line{
    width: 100%;
    border-bottom: 1px dashed #CECECE;
    margin: 24rpx 0;
  }
  .order-header-small{
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666666;
    font-size: 28rpx;
    margin-top: 16rpx;
    .order-money{
      text {
        color: #999999;
        margin-right: 4px;
      }
    }
    .coupon{
      text {
        color: #FF4D4F;
      }
    }
  }
  ::v-deep .u-cell .u-cell__body {
    height: 40px;
    padding: 10px 0;

    &.u-cell__body--large {
      height: 40px;
      padding: 0 0 10px;
    }
  }
}
</style>

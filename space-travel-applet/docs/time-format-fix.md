# 往返程航班时间格式修复

## 问题描述

在往返程航班预订时，发送到 `/order/Reservation` 接口的航班信息中，`deptTime` 和 `arrTime` 字段只包含时间部分（如 "09:15"），而缺少日期信息。这导致后端无法正确解析航班的具体出发和到达时间。

## 问题根因

1. **后端航班查询阶段**：在 `FlightFacadeImpl.java` 中，将完整的日期时间字符串拆分为：
   - `deptTime`: 只保留时间部分 `HH:mm`
   - `deptDate`: 只保留日期部分 `yyyy-MM-dd`

2. **后端订单创建阶段**：在 `OrderBizImpl.java` 中，期望 `deptTime` 是完整的日期时间格式：
   ```java
   flight.setDepartureTime(DateUtils.sdfDateTime.parse(flightDto.getDeptTime()));
   ```

## 解决方案

在前端订单提交时，重新构造完整的日期时间格式：

### 修改文件
- `space-travel-applet/pages/order/create.vue`
- `space-travel-applet/pages/order/ship-space.vue`

### 核心修复逻辑

```javascript
// 往返程时间格式修复
if (this.orderForm.isRoundTrip) {
  const departure = { ...this.orderForm.departure }
  const returnFlight = { ...this.orderForm.return }

  // 处理去程航班时间格式
  if (departure.deptTime && departure.deptDate) {
    if (departure.deptTime.length <= 5) { // 格式如 "09:15"
      departure.deptTime = `${departure.deptDate} ${departure.deptTime}:00`
    }
  }
  if (departure.arrTime && departure.deptDate) {
    if (departure.arrTime.length <= 5) { // 格式如 "11:30"
      departure.arrTime = `${departure.deptDate} ${departure.arrTime}:00`
    }
  }

  // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
  if (departure.flightBaseInfoVo) {
    if (departure.flightBaseInfoVo.deptTime && departure.deptDate) {
      if (departure.flightBaseInfoVo.deptTime.length <= 5) {
        departure.flightBaseInfoVo.deptTime = `${departure.deptDate} ${departure.flightBaseInfoVo.deptTime}:00`
      }
    }
    if (departure.flightBaseInfoVo.arrTime && departure.deptDate) {
      if (departure.flightBaseInfoVo.arrTime.length <= 5) {
        departure.flightBaseInfoVo.arrTime = `${departure.deptDate} ${departure.flightBaseInfoVo.arrTime}:00`
      }
    }
  }

  // 处理回程航班时间格式
  if (returnFlight.deptTime && returnFlight.deptDate) {
    if (returnFlight.deptTime.length <= 5) {
      returnFlight.deptTime = `${returnFlight.deptDate} ${returnFlight.deptTime}:00`
    }
  }
  if (returnFlight.arrTime && returnFlight.deptDate) {
    if (returnFlight.arrTime.length <= 5) {
      returnFlight.arrTime = `${returnFlight.deptDate} ${returnFlight.arrTime}:00`
    }
  }

  // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
  if (returnFlight.flightBaseInfoVo) {
    if (returnFlight.flightBaseInfoVo.deptTime && returnFlight.deptDate) {
      if (returnFlight.flightBaseInfoVo.deptTime.length <= 5) {
        returnFlight.flightBaseInfoVo.deptTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.deptTime}:00`
      }
    }
    if (returnFlight.flightBaseInfoVo.arrTime && returnFlight.deptDate) {
      if (returnFlight.flightBaseInfoVo.arrTime.length <= 5) {
        returnFlight.flightBaseInfoVo.arrTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.arrTime}:00`
      }
    }
  }
}
```

### 数据流确保

在 `ship-space.vue` 中确保日期字段正确传递：
```javascript
// 去程使用搜索表单中的起始日期
deptDate: departureFlightInfo.deptDate || this.searchForm?.startDate || '',

// 回程使用搜索表单中的结束日期  
deptDate: returnFlightInfo.deptDate || this.searchForm?.endDate || '',
```

## 修复效果

### 修复前

```json
{
  "departure": {
    "deptTime": "09:15",
    "arrTime": "11:30",
    "flightBaseInfoVo": {
      "deptTime": "09:15",
      "arrTime": "11:30"
    }
  },
  "return": {
    "deptTime": "14:20",
    "arrTime": "16:45",
    "flightBaseInfoVo": {
      "deptTime": "14:20",
      "arrTime": "16:45"
    }
  }
}
```

### 修复后

```json
{
  "departure": {
    "deptTime": "2025-08-01 09:15:00",
    "arrTime": "2025-08-01 11:30:00",
    "flightBaseInfoVo": {
      "deptTime": "2025-08-01 09:15:00",
      "arrTime": "2025-08-01 11:30:00"
    }
  },
  "return": {
    "deptTime": "2025-08-03 14:20:00",
    "arrTime": "2025-08-03 16:45:00",
    "flightBaseInfoVo": {
      "deptTime": "2025-08-03 14:20:00",
      "arrTime": "2025-08-03 16:45:00"
    }
  }
}
```

## 测试验证

运行测试文件验证修复效果：
```bash
node test/time-format-fix.test.js
```

所有测试用例均通过，确保时间格式修复正确。

## 影响范围

- ✅ 往返程航班预订
- ✅ 单程航班预订（同时修复）
- ✅ 订单创建流程
- ✅ 后端时间解析

## 注意事项

1. 该修复同时处理了单程和往返程的时间格式问题
2. 对于跨日航班，目前使用起飞日期作为到达日期（适用于大部分国内航班）
3. 如需更精确的跨日判断，可以后续优化比较起飞时间和到达时间的逻辑

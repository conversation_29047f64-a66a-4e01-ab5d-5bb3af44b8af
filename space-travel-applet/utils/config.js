let _envVersion

// H5:
// #ifdef H5
_envVersion = 'develop'
// #endif

// 小程序：
// #ifdef MP
_envVersion = __wxConfig ? __wxConfig.envVersion : 'develop'
// #endif

const staticPath = 'https://img.yeepay.com/spaceTravel/applet'
let _contextPath

switch (_envVersion) {
  case 'develop': // 开发版
    // _contextPath = 'http://ycetest.yeepay.com:30027/space-travel-service-gateway'
    // _contextPath = 'https://qastar.yeevcc.com/space-travel-service-gateway'
    _contextPath = 'https://ncstar.yeevcc.com/space-travel-service-gateway'
    // _contextPath = 'http://localhost:8080/space-travel-service-gateway'
    break
  case 'trial': // 体验版
    _contextPath = 'https://ncstar.yeevcc.com/space-travel-service-gateway'
    break
  case 'release': // 正式版
    _contextPath = 'https://star.yeevcc.com/space-travel-service-gateway'
    break
}

const contextPath = _contextPath
const envVersion = _envVersion

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBDGUhZ+D/tKuTwtzV9KWjMk7pJL/E+OzIjFhofyj7UE5+RU9g3/Us1YSgKSgkX2tk+s/s+ZkF0mHgZxRKITGwDbRsUDEpyo5L9237bOMOHtukRoC/Z+JQ62sYrLd3mrfWQ+zSwcbzwxqJepjdmWX1J8HR2/ry0Ko8U79Fjiy91wIDAQAB'

export {
  envVersion,
  staticPath,
  contextPath,
  publicKey,
}


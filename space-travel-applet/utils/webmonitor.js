!(function (e) {
  const t = {}; function r(n) {
    if (t[n])
      return t[n].exports; const a = t[n] = { i: n, l: !1, exports: {} }; return e[n].call(a.exports, a, a.exports, r), a.l = !0, a.exports
  }r.m = e, r.c = t, r.d = function (e, t, n) { r.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: n }) }, r.r = function (e) { typeof Symbol != 'undefined' && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }), Object.defineProperty(e, '__esModule', { value: !0 }) }, r.t = function (e, t) {
    if (1 & t && (e = r(e)), 8 & t)
      return e; if (4 & t && typeof e == 'object' && e && e.__esModule)
      return e; const n = Object.create(null); if (r.r(n), Object.defineProperty(n, 'default', { enumerable: !0, value: e }), 2 & t && typeof e != 'string')
      for (const a in e)r.d(n, a, ((t) => { return e[t] }).bind(null, a)); return n
  }, r.n = function (e) { const t = e && e.__esModule ? function () { return e.default } : function () { return e }; return r.d(t, 'a', t), t }, r.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, r.p = '', r(r.s = 0)
}([function (e, t) {
  const r = uni; let n, a; a = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', (n = r).btoa = n.btoa || function (e) {
    for (var t, r, n, o, s = '', i = 0, u = (e = String(e)).length % 3; i < e.length;) {
      if ((r = e.charCodeAt(i++)) > 255 || (n = e.charCodeAt(i++)) > 255 || (o = e.charCodeAt(i++)) > 255)
        throw new TypeError('Failed to execute \'btoa\' on \'Window\': The string to be encoded contains characters outside of the Latin1 range.'); s += a.charAt((t = r << 16 | n << 8 | o) >> 18 & 63) + a.charAt(t >> 12 & 63) + a.charAt(t >> 6 & 63) + a.charAt(63 & t)
    } return u ? s.slice(0, u - 3) + '==='.substring(u) : s
  }; const o = r.getStorageSync('WF_CONFIG'); let s = o || { s: !0, ia: [''], wc: 40, pv: { s: true, ia: [''] }, je: { s: true, ia: [''] }, hl: { s: true, ia: [''], uh: !1, rl: 500, sl: 500 }, rl: { s: true, ia: [''] }, bl: { s: true }, lc: { s: true } }; const i = '3.1.57'; const u = 'probe.yeepay.com'; const c = `https://${u}`; const l = `${c}/server/upMog`; const f = 'CUSTOMER_PV'; const g = 'CUS_LEAVE'; const h = 'HTTP_LOG'; const d = 'JS_ERROR'; const p = 'LAST_BROWSE_DATE'; const v = 'WM_PAGE_ENTRY_TIME'; const y = 'WM_VISIT_PAGE_COUNT'; const m = 'WEBFUNNY_COOKIE'; const T = [d, f, h]; const w = function (e) { const t = new b('reload', 0, 'o', ''); t.simpleUrl = e, t.completeUrl = e, t.handleLogInfo(f, t) }; class S {
    static getCurrentPages() {
      let e = ''; try { e = getCurrentPages().length ? getCurrentPages()[0].route : '' }
      catch (t) { e = '' } return e
    }

    static getUuid() { const e = S.formatDate((new Date()).getTime(), 'yMdhms'); return `${'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (e) => { const t = 16 * Math.random() | 0; return (e == 'x' ? t : 3 & t | 8).toString(16) })}-${e}` } static getCustomerKey() { const e = S.getUuid(); let t = r.getStorageSync('monitorCustomerKey'); return t || (r.setStorage({ key: 'monitorCustomerKey', data: e }), t = e), t } static isTodayBrowse(e) { const t = r.getStorageSync(e); const n = `${(new Date()).getFullYear()}-${(new Date()).getMonth() + 1}-${(new Date()).getDate()}`; return t && n == t ? !(!t || n != t) : (r.setStorageSync(e, n), !1) } static formatDate(e, t) { const r = new Date(e).getFullYear(); let n = new Date(e).getMonth() + 1; let a = new Date(e).getDate(); let o = new Date(e).getHours(); let s = new Date(e).getMinutes(); let i = new Date(e).getSeconds(); return n = n > 9 ? n : `0${n}`, a = a > 9 ? a : `0${a}`, o = o > 9 ? o : `0${o}`, s = s > 9 ? s : `0${s}`, i = i > 9 ? i : `0${i}`, t.replace('y', r).replace('M', n).replace('d', a).replace('h', o).replace('m', s).replace('s', i) } static getPageKey() { let e = r.getStorageSync('monitorPageKey'); const t = S.getUuid(); return e && /^[0-9a-z]{8}(-[0-9a-z]{4}){3}-[0-9a-z]{12}-\d{13}$/.test(e) || (e = t), e } static setPageKey() { r.setStorage({ key: 'monitorPageKey', data: S.getUuid() }) } static ajax(e, t, n, a, o) { r.request({ method: e, url: t, data: n, header: { 'Content-Type': 'application/json' }, success: (e) => { typeof a == 'function' && a(e) }, fail: (e) => { typeof o == 'function' && o(e) } }) } static uploadCusBehavior(e, t, n) { const a = { webMonitorId: S.getWebMonitorId(), userId: S.getCusInfo('userId'), uploadType: 'CUSTOMIZE_BEHAVIOR', happenTime: (new Date()).getTime(), behaviorType: S.b64EncodeUnicode(e), behaviorResult: S.b64EncodeUnicode(t), description: S.b64EncodeUnicode(n) }; r.request({ method: 'POST', url: `${c}/server/upMyLog`, data: { logs: [a] }, header: { 'Content-Type': 'application/json' } }) } static checkReqResLen(e = '', t = '', r) { let n = e ? JSON.stringify(e) : 'no param'; let a = t ? JSON.stringify(t) : 'no result'; const o = parseInt(r.rl) || 500; const s = parseInt(r.sl) || 500; return n && n.length > o && (n = 'too long'), a && a.length > s && (a = 'too long'), { requestText: n, responseText: a } } static encryptObj(e) { if (Array.isArray(e)) { for (var t = [], r = 0; r < e.length; ++r)t[r] = S.encryptObj(e[r]); return t } if (e instanceof Object) { t = {}; for (var r in e)t[r] = S.encryptObj(e[r]); return t } return (e += '').length > 50 && (e = `${e.substring(0, 10)}****${e.substring(e.length - 9, e.length)}`), e } static getDevice() { return r.getSystemInfoSync() } static b64EncodeUnicode(e) {
      const t = encodeURIComponent(e); try { return r.btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g, (e, t) => { return String.fromCharCode(`0x${t}`) })) }
      catch (e) { return t }
    }

    static utf8_encode(e) {
      if (!e)
        return ''; let t = ''; e = e.replace(/\r\n/g, '\n'); for (let r = 0; r < e.length; r++) { const n = e.charCodeAt(r); n < 128 ? t += String.fromCharCode(n) : n > 127 && n < 2048 ? (t += String.fromCharCode(n >> 6 | 192), t += String.fromCharCode(63 & n | 128)) : (t += String.fromCharCode(n >> 12 | 224), t += String.fromCharCode(n >> 6 & 63 | 128), t += String.fromCharCode(63 & n | 128)) } return t
    }

    static setWfCookie(e, t, n) {
      const a = { data: t, expires: n }; const o = r.getStorageSync(m); if (o) { o[e] = a, r.setStorage(m, o) }
      else { const s = {}; s[e] = a, r.setStorage(m, s) }
    }

    static getWfCookie(e) {
      const t = r.getStorageSync(m); if (t) {
        const n = t[e]; if (!n)
          return ''; const a = parseInt(n.expires, 10); return (new Date()).getTime() > a ? (delete t[e], r.setStorage(m, t), '') : n.data
      } return ''
    }

    static getCusInfo(e) {
      if (!e)
        return ''; const t = r.getStorageSync('wmUserInfo'); const n = (t ? JSON.parse(t) : {})[e]; return n || ''
    }

    static getWebMonitorId() { let e = 'webfunny_20230529_100710_pro'; if (e.includes('_pro')) { const t = S.getCusInfo('env'); t && (e = e.replace('_pro', `_${t}`)) } return e } static checkIgnore(e, t) {
      if (!t)
        return !0; for (var r = t.replace(/ /g, ''), n = s[e].ia || [], a = !0, o = 0; o < n.length; o++) { const i = n[o].replace(/ /g, ''); if (i && r.includes(i)) { a = !1; break } } return a
    }
  } class C {constructor() { const e = S.getCurrentPages(); this.wmVersion = i, this.happenTime = (new Date()).getTime(), this.webMonitorId = S.getWebMonitorId(), this.simpleUrl = e, this.completeUrl = e, this.customerKey = S.getCustomerKey(), this.userId = S.getCusInfo('userId'), this.projectVersion = S.getCusInfo('projectVersion') || '', this.firstUserParam = S.b64EncodeUnicode(S.getCusInfo('userTag') || ''), this.secondUserParam = S.b64EncodeUnicode(S.getCusInfo('secondUserParam') || '') }handleLogInfo(e, t) { const n = r.getStorageSync(e); const a = n || []; a.push(t), r.setStorageSync(e, a) }} class b extends C {constructor(e, t, r, n) { super(); const a = S.getDevice(); this.uploadType = f, this.pageKey = S.getPageKey(), this.deviceName = a.model, this.os = a.system, this.browserName = a.platform, this.browserVersion = a.version, this.deviceSize = `${a.screenWidth}×${a.screenHeight}`, this.monitorIp = '', this.country = '', this.province = '', this.city = '', this.loadType = e, this.loadTime = t, this.newStatus = r, this.referrer = (n || '').split('?')[0].replace('#', '') }} class x {constructor(e) { const t = S.getCurrentPages(); this.uploadType = g, this.webMonitorId = S.getWebMonitorId(), this.leaveType = e, this.happenTime = (new Date()).getTime(), this.simpleUrl = t, this.customerKey = S.getCustomerKey() }} class I extends C {constructor(e, t, r, n, a) { super(); const o = S.getDevice(); this.uploadType = e, this.infoType = t, this.pageKey = S.getPageKey(), this.deviceName = o.model, this.os = o.system, this.browserName = o.platform, this.browserVersion = o.version, this.monitorIp = '', this.country = 'china', this.province = '', this.city = '', this.simpleErrorMessage = S.b64EncodeUnicode(r), this.errorMessage = S.b64EncodeUnicode(n), this.errorStack = S.b64EncodeUnicode(a), this.browserInfo = '' }} class O extends C {constructor(e, t, r, n, a, o, s, i, u) { super(), this.uploadType = e, this.method = t ? t.toLowerCase() : 'get', this.httpUrl = S.b64EncodeUnicode(r), this.status = n, this.statusText = a, this.statusResult = o, this.requestText = S.b64EncodeUnicode(s), this.responseText = S.b64EncodeUnicode(i), this.loadTime = u }} let _ = ''; function P() {
    if (getCurrentPages().length) {
      const e = S.getCurrentPages(); _ != e && (!(function () {
        const e = S.getCurrentPages(); if (e) {
          const t = S.isTodayBrowse(p); const n = (new Date()).getTime(); r.setStorageSync(v, n); let a = null; const o = S.formatDate(n, 'y-M-d'); const s = e; const i = r.getStorageSync(y); if (i) { const u = i.split('$$$'); const c = u[0]; const g = u[1]; const h = parseInt(u[2], 10); o == g ? s != c && h == 1 && (r.setStorageSync(y, `${s}$$$${o}$$$2`), a = new x(2)) : (r.setStorageSync(y, `${s}$$$${o}$$$1`), a = new x(1)) }
          else { r.setStorageSync(y, `${s}$$$${o}$$$1`), a = new x(1) } const d = r.getStorageSync('monitorCustomerKey'); if (d) { var m = ''; const T = d ? d.match(/\d{14}/g) : []; if (T && T.length > 0) { const w = T[0].match(/\d{2}/g); const C = `${w[0] + w[1]}-${w[2]}-${w[3]} ${w[4]}:${w[5]}:${w[6]}`; const I = new Date(C).getTime(); m = (new Date()).getTime() - I > 300 ? t == 0 ? 'o_uv' : 'o' : 'n_uv' } }
          else { m = 'n_uv', S.getCustomerKey() } const O = new b('reload', 0, m, ''); if (m === 'n_uv' || m === 'o_uv') { const _ = [O, a]; S.ajax('POST', l, _) }
          else { O.handleLogInfo(f, O) }
        }
      }()), _ = e)
    }
  } function D(e, t, r, n, a, o) { P(); let s = t || ''; let i = o || ''; let u = ''; let c = ''; (s.length >= 1e3 && (s = s.substring(0, 999)), i.length >= 3e3 && (i = i.substring(0, 2999)), s.length >= 80 ? c = s.substring(0, 80) : s.length > 0 && s.length < 80 && (c = s), s) && (u = typeof i == 'string' ? i.split(': ')[0].replace('"', '') : JSON.stringify(i).split(': ')[0].replace('"', '')); if (S.checkIgnore('je', s)) { const l = new I(d, e, `${u}: ${c}`, `${u}: ${s}`, i); l.handleLogInfo(d, l) } } function U() { for (var e = [], t = 0; t < T.length; t++) { const n = r.getStorageSync(T[t]); e = e.concat(n) } for (let a = 0; a < T.length; a++)r.setStorageSync(T[a], []); e.length > 0 && S.ajax('POST', l, e, (e) => { if (e && e.data) { const t = e.data; if (t && t.data && t.data.d) { const n = t.data.c; if (n) { const a = JSON.parse(n); if (r.setStorage({ key: 'WF_CONFIG', data: a }), s = a, a.s == 0) { const o = (new Date()).getTime() + 6e5; S.setWfCookie('webfunnyStart', 'p', o) } } } } }, () => {}) }uni.webfunny = function (e) {
    for (var t = s.ia, n = !1, a = S.getCurrentPages(), o = 0; o < t.length; o++) { const i = t[o].replace(/ /g, ''); if (i && a.includes(i)) { n = !0; break } } const c = S.getWfCookie('webfunnyStart') || s.s; if (c && c != 'p' && !n) {
      const g = s.wc || 40; const d = s.je; const v = s.hl; const y = uni.switchTab; uni.switchTab = function (e) {
        const t = arguments[0].success; const r = arguments[0].fail; const n = arguments[0].complete; if (!(t || r || n))
          return new Promise((t, r) => { arguments[0].success = function (...r) { S.uploadCusBehavior('switchTab', 'success', `Tab切换至：${e.url}`), t(...r) }, arguments[0].fail = function (...t) { S.uploadCusBehavior('switchTab', 'failed', `Tab切换至：${e.url}`), r(...t) }, y.apply(this, arguments) }); arguments[0].success = function (...r) { S.uploadCusBehavior('switchTab', 'success', `Tab切换至：${e.url}`), t && t(...r) }, arguments[0].fail = function (...t) { S.uploadCusBehavior('switchTab', 'failed', `Tab切换至：${e.url}`), r && r(...t) }, y.apply(this, arguments)
      }; const m = uni.navigateBack; uni.navigateBack = function (e) {
        if (arguments[0]) { const t = arguments[0].success; const r = arguments[0].fail; t && (arguments[0].success = function (...r) { S.uploadCusBehavior('navigateBack', 'success', `页面返回层数：${e.delta}`), t && t(...r) }), r && (arguments[0].fail = function (...t) { S.uploadCusBehavior('navigateBack', 'failed', `页面返回层数：${e.delta}`), r && r(...t) }), m.apply(this, arguments) }
        else { m.apply(this, arguments), S.uploadCusBehavior('navigateBack', 'success', '页面返回层数：未传参') }
      }; const T = uni.navigateTo; uni.navigateTo = function (e) {
        if (!arguments[0])
          return new Promise((t, r) => { arguments[0] && (arguments[0].success = function (...r) { S.uploadCusBehavior('navigateTo', 'success', `路由切换至：${e.url}`), w(e.url), t(...r) }, arguments[0].fail = function (...t) { S.uploadCusBehavior('navigateTo', 'failed', `路由切换至：${e.url}`), r(...t) }, T.apply(this, arguments)) }); const t = arguments[0].success; const r = arguments[0].fail; t && (arguments[0].success = function (...r) { S.uploadCusBehavior('navigateTo', 'success', `路由切换至：${e.url}`), w(e.url), t && t(...r) }), r && (arguments[0].fail = function (...t) { S.uploadCusBehavior('navigateTo', 'failed', `路由切换至：${e.url}`), r && r(...t) }), T.apply(this, arguments)
      }; const C = uni.redirectTo; uni.redirectTo = function (e) {
        if (!arguments[0])
          return new Promise((t, r) => { arguments[0] && (arguments[0].success = function (...r) { S.uploadCusBehavior('redirectTo', 'success', `重定向至：${e.url}`), t(...r) }, arguments[0].fail = function (...t) { S.uploadCusBehavior('redirectTo', 'failed', `重定向至：${e.url}`), r(...t) }, C.apply(this, arguments)) }); const t = arguments[0].success; const r = arguments[0].fail; t && (arguments[0].success = function (...r) { S.uploadCusBehavior('redirectTo', 'success', `重定向至：${e.url}`), t && t(...r) }), r && (arguments[0].fail = function (...t) { S.uploadCusBehavior('redirectTo', 'failed', `重定向至：${e.url}`), r && r(...t) }), C.apply(this, arguments)
      }; const x = uni.reLaunch; uni.reLaunch = function (e) {
        if (!arguments[0])
          return new Promise((t, r) => { arguments[0] && (arguments[0].success = function (...r) { S.uploadCusBehavior('reLaunch', 'success', `重启至：${e.url}`), t(...r) }, arguments[0].fail = function (...t) { S.uploadCusBehavior('reLaunch', 'failed', `重启至：${e.url}`), r(...t) }, x.apply(this, arguments)) }); const t = arguments[0].success; const r = arguments[0].fail; t && (arguments[0].success = function (...r) { S.uploadCusBehavior('reLaunch', 'success', `重启至：${e.url}`), t && t(...r) }), r && (arguments[0].fail = function (...t) { S.uploadCusBehavior('reLaunch', 'failed', `重启至：${e.url}`), r && r(...t) }), x.apply(this, arguments)
      }; let I; const _ = uni.request; uni.request = function () {
        function e(e, t, r, n, a, o, s, i, u) { if (S.checkIgnore('hl', r)) { const c = new O(e, t, r, n, a, o, s, i, u); c.handleLogInfo(e, c) } } const t = arguments[0]; if (!t.url.includes(u)) { t.startTime = (new Date()).getTime(); const r = t.method; const n = t.url; e(h, r, n, '200', 'OK', 'request', '', '', 0) } const a = arguments[0].success; const o = arguments[0].fail; const s = arguments[0].complete; if (!(a || o || s))
          return new Promise((r, n) => { arguments[0].success = function (...n) { if (!t.url.includes(u)) { const a = (new Date()).getTime() - t.startTime; const o = t.method; const s = t.url; const i = n[0]; const c = i.statusCode; console.log('请求参数：', t); const l = S.checkReqResLen(t.data, i.data, v); e(h, o, s, c, 'OK', 'response', l.requestText, l.responseText, a) }r(...n) }, arguments[0].fail = function (...r) { if (!t.url.includes(u)) { const a = (new Date()).getTime() - t.startTime; const o = t.method; const s = t.url; const i = r[0]; const c = JSON.stringify(t.data); const l = i.errMsg; e(h, o, s, 555, 'fail', 'response', c, l, a) }n(...r) }, _.apply(this, arguments) }); arguments[0].success = function (...r) { if (!t.url.includes(u)) { const n = (new Date()).getTime() - t.startTime; const o = t.method; const s = t.url; const i = r[0]; const c = i.statusCode; const l = S.checkReqResLen(t.data, i.data, v); e(h, o, s, c, 'OK', 'response', l.requestText, l.responseText, n) }a && a(...r) }, arguments[0].fail = function (...r) { if (!t.url.includes(u)) { const n = (new Date()).getTime() - t.startTime; const a = t.method; const s = t.url; const i = r[0]; const c = JSON.stringify(t.data); const l = i.errMsg; e(h, a, s, 555, 'fail', 'response', c, l, n) }o && o(...r) }, _.apply(this, arguments)
      }, d.s && (I = console.error, console.error = function (e) {
        let t = arguments[0] && arguments[0].message || e; const r = arguments[0] && arguments[0].stack; if (r) { D('on_error', t, 0, 0, 0, r) }
        else {
          if (typeof t == 'object') {
            try { t = JSON.stringify(t) }
            catch (e) { t = '错误无法解析' }
          }D('console_error', t, 0, 0, 0, `CustomizeError: ${t}`)
        } return I.apply(console, arguments)
      }); let B = (new Date()).getTime(); setInterval(() => { P(); const e = (new Date()).getTime(); const t = e - B; r.getStorageSync(h).length > 10 ? U() : t >= 200 * g && (U(), B = e) }, 1e3); const E = {
        onLaunch() {
          !(function (e) {
            const t = S.isTodayBrowse(p); const n = r.getStorageSync('monitorCustomerKey'); if (n) { var a = ''; const o = n ? n.match(/\d{14}/g) : []; if (o && o.length > 0) { const s = o[0].match(/\d{2}/g); const i = `${s[0] + s[1]}-${s[2]}-${s[3]} ${s[4]}:${s[5]}:${s[6]}`; const u = new Date(i).getTime(); a = (new Date()).getTime() - u > 300 ? t == 0 ? 'o_uv' : 'o' : 'n_uv' } }
            else { a = 'n_uv', S.getCustomerKey() } const c = new b('reload', 0, a, ''); if (c.simpleUrl = e, c.completeUrl = e, a === 'n_uv' || a === 'o_uv') { const g = [c]; S.getCusInfo('userId') ? S.ajax('POST', l, g) : setTimeout(() => { S.ajax('POST', l, g) }, 3e3) }
            else { c.handleLogInfo(f, c) }
          }('onLaunch'))
        },
        onError(e) { if (d.s) { let t = ''; typeof e == 'string' ? t = e : typeof e == 'object' && (t = e.stack || ''); const r = t.split('\n')[2]; S.getCurrentPages(), D('on_error', r, 0, 0, 0, t) } },
      }; for (const t in E) { const r = typeof e[t] == 'function' && e[t]; e[t] = function () { E[t].apply(this, arguments), r && r.apply(this, arguments) } } return e
    }
  }
}]))

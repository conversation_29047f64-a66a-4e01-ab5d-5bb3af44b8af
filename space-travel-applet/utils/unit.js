export default {
  install(Vue) {
    Vue.prototype.$getDateStr = (AddDayCount) => {
      const dd = new Date()
      dd.setDate(dd.getDate() + AddDayCount)
      const y = dd.getFullYear()
      const m = dd.getMonth() + 1
      const d = dd.getDate()
      return `${y}-${num(m)}-${num(d)}`
    }
    Vue.prototype.$formatDuring = (mss) => {
      const hours = parseInt(mss / (1000 * 60 * 60))
      const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = parseInt((mss % (1000 * 60)) / 1000)
      return [num(hours), num(minutes), num(seconds)]
    }
    Vue.prototype.$timeDiff = function timeDiff(begin_time, end_time) {
      let beginTime = (begin_time.toString()).includes('-') ? begin_time.replace(/-/g, '/') : begin_time
      let endTime = (end_time.toString()).includes('-') ? end_time.replace(/-/g, '/') : end_time
      beginTime = (new Date(beginTime).getTime()) / 1000
      endTime = (new Date(endTime).getTime()) / 1000
      let starttime
      let endtime
      if (beginTime < endTime) {
        starttime = beginTime
        endtime = endTime
      }
      else {
        starttime = endTime
        endtime = beginTime
      }
      // 计算天数
      const timediff = endtime - starttime
      const days = parseInt(timediff / 86400)
      // 计算小时数
      let remain = timediff % 86400
      const hours = parseInt(remain / 3600)
      // 计算分钟数
      remain = remain % 3600
      const mins = parseInt(remain / 60)
      const res = `${(days ? `${days}d` : '') + (hours ? `${hours}h` : '') + mins}m`
      return res
    }
    /**
     * 将整数格式化成每 3 位添加一个逗号
     * @param {Number} num 待格式化的数字
     * @returns {String} 返回格式化后的数字
     */
    Vue.prototype.$formatMoney = function (num) {
      let numPrefix = ''
      let numArr = ''
      let numDist = ''

      const _num = parseInt(num)

      // 处理负数情况
      if (_num < 0) {
        numPrefix = '-'
        numArr = String(_num).slice(1).split('').reverse()
      }
      else {
        numArr = String(_num).split('').reverse()
      }

      for (let i = 0; i < numArr.length; i++) {
        numDist += numArr[i]
        if ((i + 1) % 3 === 0 && (i + 1) < numArr.length)
          numDist += ''
      }

      return numPrefix + numDist.split('').reverse().join('')
    }
    Vue.prototype.$formatDateStr = function (date, format) {
      /**
       * 若文档中已有命名dateFormat，可用dFormat()调用
       * 年(y) 可用1-4个占位符
       * 月(m)、日(d)、小时(h)、分(M)、秒(s) 可用1-2个占位符
       * 星期(W) 可用1和多个占位符 1位: 一，多位：周一/今天
       * let date = new Date()
       * dateFormat(date, "yyyy-mm-dd hh:MM:ss")           2020-02-09 14:04:23
       * dateFormat(date, "yyyy-mm-dd hh:MM:ss WW")       2020-02-09 14:45:12 周日
       */

      if (!date)
        return ''

      // 处理iOS兼容性问题，确保日期格式正确
      let dateInit = date
      if (typeof date === 'string') {
        // 处理包含日期和时间的字符串，如 "2025-07-30 00:00:00"
        if (date.includes('-') && date.includes(' ') && date.includes(':')) {
          // 转换为iOS兼容的ISO 8601格式
          dateInit = date.replace(' ', 'T')
        }
        // 处理只包含日期的字符串，如 "2025-07-30"
        else if (date.includes('-')) {
          dateInit = date.replace(/-/g, '/')
        }
      }

      const _date = new Date(dateInit)

      // 检查日期是否有效
      if (isNaN(_date.getTime())) {
        console.warn('日期格式化失败:', date)
        return date || ''
      }
      const we = _date.getDay() // 星期
      const opt = {
        'y+': _date.getFullYear().toString(), // 年
        'm+': (_date.getMonth() + 1).toString(), // 月(月份从0开始，要+1)
        'd+': _date.getDate().toString(), // 日
        'h+': _date.getHours().toString(), // 时
        'M+': _date.getMinutes().toString(), // 分
        's+': _date.getSeconds().toString(), // 秒
      }
      const week = { // 中文数字 (星期)
        0: '日',
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六',
      }
      if (/(W+)/.test(format)) {
        // const now = new Date(); let dateStr
        // if (now.getFullYear() === _date.getFullYear() && now.getMonth() === _date.getMonth()) {
        //   if (now.getDate() === _date.getDate())
        //     dateStr = '今天'
        //   else if (now.getDate() + 1 === _date.getDate())
        //     dateStr = '明天'
        //   else if (now.getDate() + 2 === _date.getDate())
        //     dateStr = '后天'
        // }
        // format = format.replace(RegExp.$1, (RegExp.$1.length > 1 ? (dateStr || `周${week[we]}`) : week[we]))
        format = format.replace(RegExp.$1, (RegExp.$1.length > 1 ? (`周${week[we]}`) : week[we]))
      }

      for (const k in opt) {
        const r = new RegExp(`(${k})`).exec(format)
        if (r) {
          // 若输入的长度不为1，则前面补零
          format = format.replace(r[1], (RegExp.$1.length === 1 ? opt[k] : opt[k].padStart(RegExp.$1.length, '0')))
        }
      }
      return format
    }

    /**
     * 销量格式化
     * 万以下显示具体数字，例如9999
     * 万以上显示：X万+，例如19999，显示为1万+
     * 十万以上显示：10万+，10几万+
     * @param number 数字
     */
    Vue.prototype.$formatAcount = function (number) {
      let acount
      const _number = parseInt(number)
      if (_number < 10000)
        acount = _number
      else if (_number >= 10000)
        acount = `${parseInt(_number / 10000)}万+`

      return acount
    }
    Vue.prototype.$dataCompare = function (data1, data2) {
      const _data1 = data1.replace(/\-/g, '/')
      const _data2 = data2.replace(/\-/g, '/')
      return Date.parse(_data1) > Date.parse(_data2)
    }

    Vue.prototype.$getWeek = function (date) {
      if (!date)
        return ''

      const dateInit = (date.toString()).includes('-') ? date.replace(/-/g, '/') : date
      const _date = new Date(dateInit)
      const week = {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
      }

      return week[_date.getDay()]
    }

    Vue.prototype.$formatPhoneNumber = function (value) {
      const len = value.length
      if (len > 3 && len < 8)
        value = value.replace(/^(\d{3})/g, '$1 ')
      else if (len > 8 && len < 11)
        value = value.replace(/^(\d{3})(\d{4})/g, '$1 $2 ')
      else if (len >= 11)
        value = value.replace(/^(\d{3})(\d{4})(\d{4})(\d*)/g, '$1 $2 $3')
      return value
    }

    Vue.prototype.$formatIdCard = function (value) {
      const len = value.length
      if (len > 6 && len < 14)
        value = value.replace(/^(\d{6})/g, '$1 ')
      else if (len > 14 && len < 18)
        value = value.replace(/^(\d{6})(\d{8})/g, '$1 $2 ')
      else if (len >= 18)
        value = value.replace(/^(\d{6})(\d{8})(\d{4})(\d*)/g, '$1 $2 $3')
      return value
    }

    Vue.prototype.$formatTickNum = function (value) {
      if (!value)
        return ''
      return value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ')
    }

    // 通过身份证号计算年龄、性别、出生日期
    Vue.prototype.$getIdCardDate = function (userCard) {
      // 出生日期
      const birth = `${userCard.substring(6, 10)}-${userCard.substring(10, 12)}-${userCard.substring(12, 14)}`
      // 性别
      const sex = parseInt(userCard.substr(16, 1)) % 2 === 1 ? 'M' : 'F'
      // 年龄
      const myDate = new Date()
      const yearBirth = userCard.substring(6, 10)
      const monthBirth = userCard.substring(10, 12)
      const dayBirth = userCard.substring(12, 14)
      const monthNow = myDate.getMonth() + 1
      const dayNow = myDate.getDay()
      let age = myDate.getFullYear() - yearBirth
      if (monthNow < monthBirth || (monthNow === monthBirth && dayNow < dayBirth))
        age--

      return {
        birth,
        sex,
        age,
      }
    }
  },
}

/**
 * 不满两位 前面补0
 * @param number 数字
 */
function num(number) {
  return number < 10 ? `0${number}` : number
}

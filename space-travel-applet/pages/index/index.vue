<script>
import { mapState } from 'vuex'
import { ADULT_TYPE, ADULT_TYPE_ZH } from '@/utils/type'

export default {
  data() {
    return {
      showStartDate: false,
      showEndDate: false,
      showRoomPicker: false,
      bannerSrc: 'https://img.yeepay.com/spaceTravel/static/img/<EMAIL>',
      animation: false,
      calendarPriceList: [],
      searchForm: {
        flightName: '',
        startCity: '',
        endCity: '',
        startCityCode: '',
        endCityCode: '',
        startCityName: '',
        endCityName: '',
        startCityAirport: '',
        endCityAirport: '',
        startDate: '',
        endDate: '',
        shipSpace: 'economy',
        peopleType: '成人',
        journeyType: 'OW', // OW-单程, RT-往返
      },
      // 优惠类型选项
      priceTypeOptions: [
        { value: 'EVERY_DISCOUNT', label: '单单减优惠' },
        { value: 'YEEPAY_DISCOUNT', label: '易宝优惠券' },
        { value: 'CUSTOMER_DISCOUNT', label: '商户优惠券' },
      ],
      selectedPriceType: 'EVERY_DISCOUNT', // 默认选择单单减优惠
      shipSpaceOptions: {
        economy: '经济舱',
        first: '公务舱/头等舱',
      },
      journeyTypeOptions: [
        { label: '单程', value: 'OW' },
        { label: '往返', value: 'RT' },
      ],
      fileList: [],
      showErrorModal: false,
      info: [{
        content: '内容 A',
      }],
      value: 1,
      showPeopleSelect: false,
      peopleNum: {
        adult: 0,
        child: 0,
      },
      adult_zh: ADULT_TYPE_ZH,
      type_adult: ADULT_TYPE,
      adultType: '',
      searchTypeObj: {
        startCity: '',
        endCity: '',
      },
    }
  },
  computed: {
    ...mapState(['userInfo']),
    shipSpaceStr() {
      return this.shipSpaceOptions[this.searchForm.shipSpace]
    },
    disabled() {
      const baseCheck = (!this.searchForm.endCityCode && !this.searchForm.endCityAirport) || (!this.searchForm.startCityCode && !this.searchForm.startCityAirport) || !this.searchForm.startDate
      // 如果是往返程，还需要检查回程日期
      if (this.searchForm.journeyType === 'RT')
        return baseCheck || !this.searchForm.endDate

      return baseCheck
    },
    isRoundTrip() {
      return this.searchForm.journeyType === 'RT'
    },

  },
  onLoad({ searchType, airportCityCode, airportCityName, airportCode }) {
    console.log('DEBUG - onLoad 接收参数:', { searchType, airportCityCode, airportCityName, airportCode })
    if (searchType) {
      console.log('DEBUG - 设置城市信息:', {
        type: searchType,
        cityName: airportCityName,
        cityCode: airportCityCode,
        airportCode,
      })
      this.searchForm[searchType] = airportCityName
      this.searchForm[`${searchType}Code`] = airportCityCode
      this.searchForm[`${searchType}Airport`] = airportCode
    }
    this.searchForm.startDate = this.searchForm.startDate || this.$getDateStr(0)
    // 默认回程日期为去程日期+1天
    this.searchForm.endDate = this.searchForm.endDate || this.$getDateStr(1)
    this.showErrorModal = true
  },
  methods: {
    changeflight() {
      if (this.animation)
        return
      this.animation = true
      setTimeout(() => {
        this.animation = false
      }, 200);
      [this.searchForm.startCity, this.searchForm.endCity] = [this.searchForm.endCity, this.searchForm.startCity]
      if (this.searchForm.startCityCode && this.searchForm.endCityCode) {
        [this.searchForm.startCityCode, this.searchForm.endCityCode] = [this.searchForm.endCityCode, this.searchForm.startCityCode]
      }
      else if (this.searchForm.startCityAirport && this.searchForm.endCityAirport) {
        [this.searchForm.startCityAirport, this.searchForm.endCityAirport] = [this.searchForm.endCityAirport, this.searchForm.startCityAirport]
      }
      else if (this.searchForm.startCityCode && this.searchForm.endCityAirport) {
        this.searchForm.startCityAirport = this.searchForm.endCityAirport
        this.searchForm.endCityAirport = null
        this.searchForm.endCityCode = this.searchForm.startCityCode
        this.searchForm.startCityCode = null
      }
      else if (this.searchForm.startCityAirport && this.searchForm.endCityCode) {
        this.searchForm.startCityCode = this.searchForm.endCityCode
        this.searchForm.endCityCode = null
        this.searchForm.endCityAirport = this.searchForm.startCityAirport
        this.searchForm.startCityAirport = null
      }
    },
    changeStartDate() {
      this.showStartDate = true
    },
    changeEndDate() {
      this.showEndDate = true
    },

    showRoomPickerFun() {
      this.showRoomPicker = true
    },
    closeRoomPickerFun() {
      this.showRoomPicker = false
    },
    toRoute() {
      if (!this.searchForm.startCityCode && !this.searchForm.startCityAirport)
        return this.$showToast('请选择出发地')

      if (!this.searchForm.endCityCode && !this.searchForm.endCityAirport)
        return this.$showToast('请选择目的地')

      if (!this.searchForm.startDate)
        return this.$showToast('请选择出发日期')

      if (this.searchForm.journeyType === 'RT' && !this.searchForm.endDate)
        return this.$showToast('请选择回程日期')

      if (this.searchForm.journeyType === 'RT' && this.searchForm.endDate <= this.searchForm.startDate)
        return this.$showToast('回程日期不能早于或等于去程日期')

      uni.navigateTo({
        url: `/pages/order/route?data=${JSON.stringify({
          ...this.searchForm,
          peopleType: this.peopleNum,
          selectedPriceType: this.selectedPriceType, // 传递选中的优惠类型
        })}`,
      })
    },
    getStartDate({ startStr }) {
      this.searchForm.startDate = startStr.dateStr
      // 如果是往返程且回程日期早于新的去程日期，自动调整回程日期
      if (this.searchForm.journeyType === 'RT' && this.searchForm.endDate <= this.searchForm.startDate)
        this.searchForm.endDate = this.$getDateStr(1, this.searchForm.startDate)
    },
    getEndDate({ startStr }) {
      const selectedEndDate = startStr.dateStr
      // 验证返程日期不能早于去程日期
      if (this.searchForm.journeyType === 'RT' && this.$dataCompare(this.searchForm.startDate, selectedEndDate)) {
        this.$showToast('返程日期不能早于去程日期，请重新选择')
        return
      }
      this.searchForm.endDate = selectedEndDate
    },
    showPeopleCheck() {
      this.showPeopleSelect = true
    },
    closePeopleCheck() {
      this.showPeopleSelect = false
    },
    changeCity(type) {
      console.log('DEBUG - changeCity 调用:', type)
      uni.navigateTo({
        url: `/pages_common/select-city/select-city?searchType=${type}`,
      })
    },
    changePeopleType({ peopleNum, adultType }) {
      this.peopleNum = peopleNum
      this.adultType = adultType
      this.showPeopleSelect = false
    },
    selectJourneyType(type) {
      this.searchForm.journeyType = type
      // 如果切换到单程，清空回程日期显示（但保留数据）
      // 如果切换到往返，确保回程日期不早于去程日期
      if (this.searchForm.journeyType === 'RT') {
        if (!this.searchForm.endDate || this.searchForm.endDate <= this.searchForm.startDate)
          this.searchForm.endDate = this.$getDateStr(1, this.searchForm.startDate)
      }
    },
  },
}
</script>

<template>
  <view class="page-wrap">
    <view class="page-content">
      <image
        :src="bannerSrc" class="banner" mode="aspectFill"
      />
      <view class="air-info">
        <u-cell-group>
          <!-- 行程类型选择 - 修改为tab选择效果 -->
          <view class="journey-type-tabs">
            <view
              v-for="(item, index) in journeyTypeOptions"
              :key="index"
              class="tab-item"
              :class="{ active: searchForm.journeyType === item.value }"
              @click="selectJourneyType(item.value)"
            >
              {{ item.label }}
            </view>
          </view>

          <u-cell :border="false">
            <template #title>
              <view class="cell-title flight-info">
                <view class="start" :class="{ animate: animation }">
                  <view class="item" :class="{ empty: !searchForm.startCity }" @click="changeCity('startCity')">
                    {{ searchForm.startCity || '选择出发地' }}
                  </view>
                </view>
                <view style="margin: 0 10px;">
                  <uni-icons
                    custom-prefix="iconfont"
                    type="icon-hangbanqiehuan"
                    size="20"
                    color="#8954BA"
                    @click="changeflight"
                  />
                </view>
                <view class="end" :class="{ animate: animation }">
                  <view class="item" :class="{ empty: !searchForm.endCity }" @click="changeCity('endCity')">
                    {{ searchForm.endCity || '选择目的地' }}
                  </view>
                </view>
              </view>
            </template>
          </u-cell>
          <u-cell :border="false" :is-link="true" @click="changeStartDate">
            <template #title>
              <view class="cell-title">
                出发日期
                <text class="week">
                  {{ $getWeek(searchForm.startDate) }}
                </text>
              </view>
            </template>
            <template #value>
              <text class="cell-value">
                {{ searchForm.startDate }}
              </text>
            </template>
          </u-cell>
          <!-- 往返程回程日期选择 -->
          <u-cell v-if="isRoundTrip" :border="false" :is-link="true" @click="changeEndDate">
            <template #title>
              <view class="cell-title">
                返程日期
                <text class="week">
                  {{ $getWeek(searchForm.endDate) }}
                </text>
              </view>
            </template>
            <template #value>
              <text class="cell-value">
                {{ searchForm.endDate }}
              </text>
            </template>
          </u-cell>
          <u-cell :border="false" :is-link="true" @click="showRoomPickerFun">
            <template #title>
              <view class="cell-title">
                选择舱位
              </view>
            </template>
            <template #value>
              <text class="cell-value">
                {{ shipSpaceStr }}
              </text>
            </template>
          </u-cell>
          <u-cell :border="false" :is-link="true" @click="showPeopleCheck">
            <template #title>
              <view class="cell-title">
                选择旅客类型
              </view>
            </template>
            <template #value>
              <text class="cell-value">
                <text v-if="peopleNum.adult || peopleNum.child">
                  <text v-if="peopleNum.adult">
                    <text class="cell-value-number">
                      {{ peopleNum.adult }}
                    </text>
                    <text class="cell-value-type">
                      {{ adultType === type_adult.YOUNG ? adult_zh.YOUNG : adultType === type_adult.OLD ? adult_zh.OLD : adult_zh.DEFAULT }}
                    </text>
                  </text>
                  <text v-if="peopleNum.child">
                    <text class="cell-value-number">
                      {{ peopleNum.child }}
                    </text>
                    <text class="cell-value-type">
                      儿童
                    </text>
                  </text>
                </text>
                <text v-else>
                  <text class="cell-value-text">
                    获取更精准低价
                  </text>
                </text>
              </text>
            </template>
          </u-cell>
          <u-cell :border="false">
            <template #title>
              <view class="search-btn">
                <u-button
                  type="primary" :disabled="disabled"
                  :custom-style="{ fontSize: '16px', height: '44px', color: '#ffffff' }" @click="toRoute"
                >
                  查 询
                </u-button>
              </view>
            </template>
          </u-cell>
        </u-cell-group>
      </view>
    </view>
    <u-popup round="20" :show="showRoomPicker" @close="closeRoomPickerFun">
      <view class="popup-wrap">
        <view class="popup-header">
          选择舱位
        </view>
        <view class="popup-content">
          <u-radio-group v-model="searchForm.shipSpace" placement="column" border-bottom @change="closeRoomPickerFun">
            <u-radio
              v-for="(item, index) in shipSpaceOptions" :key="index" :active-color="primaryColor"
              :custom-style="{ marginBottom: '8px' }" :label="item" :name="index"
            />
          </u-radio-group>
        </view>
      </view>
    </u-popup>
    <calendar-popup
      v-model="showStartDate" :mode="1" :day-count="1" :start-date="searchForm.startDate"
      :end-date="searchForm.startDate" :calendar-price-list="calendarPriceList" @callback="getStartDate"
    />
    <calendar-popup
      v-model="showEndDate" :mode="1" :day-count="1" :start-date="searchForm.endDate"
      :end-date="searchForm.endDate" :calendar-price-list="calendarPriceList" @callback="getEndDate"
    />
    <numberbox-popup
      :show="showPeopleSelect" @close="closePeopleCheck"
      @confirm="changePeopleType"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-wrap {
  .page-content {
    width: 100%;
    .banner{
      position: relative;
      top: 0;
      z-index: 1;
      width: 100%;
    }
    .air-info {
      position: relative;
      z-index: 2;
      top: -114rpx;
      margin: 0 24rpx;
      box-sizing: border-box;
      padding: 30px 20px;
      background: #FFFFFF;
      box-shadow: 0px 2px 6px 0px rgba(202, 202, 202, 0.42);
      border-radius: 8px;

      .journey-type-tabs {
        display: flex;
        background: #F8F8F8;
        border-radius: 24px;
        padding: 4px;
        margin-bottom: 20px;

        .tab-item {
          flex: 1;
          text-align: center;
          padding: 8px 0;
          border-radius: 20px;
          font-size: 14px;
          color: #666666;
          transition: all 0.3s ease;
          cursor: pointer;

          &.active {
            background: #FFFFFF;
            color: #333333;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .cell-title {
        font-size: 18px;
        font-weight: bold;
        color: #333333;

        .week {
          margin-left: 4px;
          font-size: 12px;
          font-weight: 400;
          color: #888888;
          line-height: 16px;
        }

        .start,
        .end {
          width: 120px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          transition: 0.2s;

          .item {
            white-space: nowrap;
            overflow: hidden;

            &.empty {
              color: #D8D8D8 !important;
            }
          }

          &.animate {
            transform: translateX(-250%);
          }
        }

        .start {
          &.animate {
            transform: translateX(100%);
          }
        }

        .end {
          text-align: right;

          &.animate {
            transform: translateX(-100%);
          }
        }

        &.flight-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }

      .cell-value {
        font-size: 28rpx;
        font-weight: 400;
        color: #999;
        line-height: 40rpx;

        &-number {
          color: #333333;
        }

        &-type {
          margin-right: 8rpx;
        }
        &-text {
          color: $u-primary;
          margin-right: 20rpx;
        }
      }

      ::v-deep .u-cell .u-cell__body {
        padding: 10px 0px;
        min-height: 54px;
      }

      .cell-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .search-btn {
        margin-top: 12px;
      }
    }

    .swiper-box {
      height: 320rpx;
      border-radius: 16rpx;
      border: 2rpx solid #979797;
      overflow: hidden;
    }

    .swiper-item {
      height: 100%;
      width: 100%;
      background-color: #888888;
    }
  }

}

.popup-wrap {
  min-height: 200px;

  .popup-content {
    padding: 20px;
  }
}
</style>


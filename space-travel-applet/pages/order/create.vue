<script>
import { CERT_TYPE, CERT_TYPE_ZH, PERSON_TYPE, PERSON_TYPE_ZH } from '@/utils/type'
import PriceDetail from '@/components/price-detail/price-detail.vue'
export default {
  components: {
    PriceDetail,
  },
  data() {
    return {
      activeSrc: `${getApp().globalData.staticPath}/images/icon-active.png`,
      wechatPaySrc: `${getApp().globalData.staticPath}/images/wechat-pay.png`,
      userList: [], // 确保初始化为空数组
      showList: [], // 确保初始化为空数组
      orderForm: {
        isAgree: false,
        flight: null,
        // 往返程相关数据
        departure: null, // 去程航班信息
        return: null, // 回程航班信息
        isRoundTrip: false, // 是否往返程
        totalPrice: 0, // 往返程总价
        contact: {
          contactEmail: '',
          contactMobile: '',
          contactName: '',
          phoneType: '+86',
        },
        passengers: [], // 确保初始化为空数组
      },
      checkedArgree: [], // 确保初始化为空数组
      showOrderInfo: false,
      showOrderDetail: false,
      showOperationUser: false,
      payBtnLoading: false,
      loading: false,
      operationUserType: 'add',
      timer: '',
      timeNumber: 50,
      showCountryPicker: false,
      showErrorModal: false,
      countryColumns: this.$appMap.countryColumns,
      searchForm: {},
      errorContent: '',
      errorTimer: '',
      agreementList: [], // 确保初始化为空数组
      isFirst: true,
    }
  },
  computed: {
    activeItem() {
      return (val) => {
        const passengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []
        return passengers.findIndex(item => item && item.id === val) >= 0
      }
    },
    disabled() {
      const passengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []
      const hasPassengers = passengers.length > 0
      const hasContactName = !!this.orderForm.contact.contactName
      const hasContactMobile = !!this.orderForm.contact.contactMobile
      const isAgreed = !!this.orderForm.isAgree

      const isDisabled = !hasPassengers || !hasContactName || !hasContactMobile || !isAgreed

      // 添加调试信息
      if (isDisabled) {
        console.log('🔒 提交按钮被禁用，原因检查:')
        console.log('   - 乘客数量:', passengers.length, '有乘客:', hasPassengers)
        console.log('   - 联系人姓名:', this.orderForm.contact.contactName, '有姓名:', hasContactName)
        console.log('   - 联系电话:', this.orderForm.contact.contactMobile, '有电话:', hasContactMobile)
        console.log('   - 同意协议:', this.orderForm.isAgree, '已同意:', isAgreed)
      }

      return isDisabled
    },
    ticketTotalAmount() {
      if (this.orderForm.isRoundTrip) {
        // 往返程价格计算：仅票价含税，不含机建燃油
        const ticketPrice = this.totalTicketPrice

        console.log('💰 往返程价格计算详情:')
        console.log('   - totalTicketPrice:', ticketPrice)
        console.log('   - departure存在:', !!this.orderForm.departure)
        console.log('   - return存在:', !!this.orderForm.return)

        // 如果有去程和回程数据，即使没有乘客也显示基础价格
        if (this.orderForm.departure && this.orderForm.return) {
          if (ticketPrice > 0) {
            return ticketPrice
          }
          else {
            // 尝试备用计算方法（仅票价含税）
            console.log('⚠️ 主要计算方法失败，尝试备用方法')
            const backupTotal = this.calculateRoundTripTicketPriceOnly()
            console.log('   - 备用方法计算结果:', backupTotal)
            return backupTotal > 0 ? backupTotal : 0
          }
        }
        else {
          console.log('⚠️ 缺少往返程数据')
          return '--'
        }
      }
      else if (this.orderForm.flight) {
        // 单程价格计算：仅票价含税，不含机建燃油
        return this.singleTicketPrice
      }
      else {
        return '--'
      }
    },
    // 去程价格计算
    departurePrice() {
      if (!this.orderForm.departure)
        return 0
      return this.calculateFlightPrice(this.orderForm.departure)
    },
    // 回程价格计算
    returnPrice() {
      if (!this.orderForm.return)
        return 0
      return this.calculateFlightPrice(this.orderForm.return)
    },

    // 往返程票价总计
    totalTicketPrice() {
      if (!this.orderForm.isRoundTrip)
        return 0
      const departureTicketPrice = this.calculateFlightTicketPrice(this.orderForm.departure)
      const returnTicketPrice = this.calculateFlightTicketPrice(this.orderForm.return)
      return departureTicketPrice + returnTicketPrice
    },

    // 往返程机建燃油费总计
    totalAirportAndFuel() {
      if (!this.orderForm.isRoundTrip)
        return 0
      const departureAirportAndFuel = this.calculateFlightAirportAndFuel(this.orderForm.departure)
      const returnAirportAndFuel = this.calculateFlightAirportAndFuel(this.orderForm.return)
      return departureAirportAndFuel + returnAirportAndFuel
    },

    // 单程票价总计
    singleTicketPrice() {
      if (this.orderForm.isRoundTrip || !this.orderForm.flight)
        return 0
      return this.calculateFlightTicketPrice(this.orderForm.flight)
    },

    // 单程机建燃油费总计
    singleAirportAndFuel() {
      if (this.orderForm.isRoundTrip || !this.orderForm.flight)
        return 0
      return this.calculateFlightAirportAndFuel(this.orderForm.flight)
    },
    passagersStr() {
      const passengers = (this.orderForm.passengers || []).map((item) => {
        return (item && item.firstName || '') + (item && item.lastName || '')
      })
      return passengers.join(',')
    },
    agreementActive() {
      return this.orderForm.isAgree
    },
    passengersAudlt() {
      let audlt = 0
      ;(this.orderForm.passengers || []).map((item) => {
        if (item.personType === PERSON_TYPE.ADULT)
          audlt += 1
      })
      return audlt
    },
    passengersChild() {
      let child = 0
      ;(this.orderForm.passengers || []).map((item) => {
        if (item.personType === PERSON_TYPE.CHILD)
          child += 1
      })
      return child
    },
    passengersNumString() {
      if (this.passengersAudlt || this.passengersChild)
        return `${this.passengersAudlt ? `${this.passengersAudlt}成人` : ''} ${this.passengersChild ? `${this.passengersChild}儿童` : ''}`

      else
        return ''
    },
    passengersNum() {
      return this.passengersAudlt + this.passengersChild
    },
    // 去程数据，将经停信息替换为"去程"
    departureData() {
      if (!this.orderForm.departure)
        return null
      return {
        ...this.orderForm.departure,
        stop: '去程',
      }
    },
    // 回程数据，将经停信息替换为"回程"
    returnData() {
      if (!this.orderForm.return)
        return null
      return {
        ...this.orderForm.return,
        stop: '回程',
      }
    },
  },
  watch: {
    showOrderInfo(val) {
      if (val) {
        this.timeNumber = 0
        this.timer = setInterval(() => {
          this.timeNumber += 1
          if (this.timeNumber > 50) {
            clearInterval(this.timer)
            this.timeNumber = 50
            this.showOrderInfo = false
          }
        }, 400)
      }
      else {
        clearInterval(this.timer)
        this.timer = ''
        this.timeNumber = 50
      }
    },
  },
  mounted() {
    // 添加全局错误处理和数据状态检查
    try {
      this.$nextTick(() => {
        console.log('🏗️ create页面DOM渲染完成，检查数据状态:')
        console.log('   - isRoundTrip:', this.orderForm.isRoundTrip)
        console.log('   - passengers类型:', Array.isArray(this.orderForm.passengers) ? 'Array' : typeof this.orderForm.passengers)
        console.log('   - passengers长度:', this.orderForm.passengers?.length || 0)
        console.log('   - showList类型:', Array.isArray(this.showList) ? 'Array' : typeof this.showList)
        console.log('   - showList长度:', this.showList?.length || 0)
        console.log('   - userList类型:', Array.isArray(this.userList) ? 'Array' : typeof this.userList)
        console.log('   - userList长度:', this.userList?.length || 0)
        console.log('   - loading状态:', this.loading)

        // 布局状态检查
        if (this.orderForm.isRoundTrip)
          console.log('🎨 往返程模式：乘客选择区域应该在航班信息下方')

        // 如果5秒后仍然在loading状态，进行兜底处理
        setTimeout(() => {
          if (this.loading) {
            console.warn('⚠️ 检测到异常的loading状态，进行兜底处理')
            this.loading = false
            if (!Array.isArray(this.userList))
              this.userList = []

            if (!Array.isArray(this.showList))
              this.showList = []

            console.log('🔧 兜底处理完成，当前状态:')
            console.log('   - loading:', this.loading)
            console.log('   - userList长度:', this.userList.length)
            console.log('   - showList长度:', this.showList.length)
          }
        }, 5000)
      })
    }
    catch (error) {
      console.error('❌ mounted阶段错误:', error)
    }
  },
  async onLoad(params) {
    uni.setStorageSync('edit_passenger_id', '')

    // 页面加载时立即查询乘客列表
    console.log('📱 页面加载，开始查询乘客列表')

    // 先查询乘客列表，确保用户信息可用
    await this.initializePassengerData()

    try {
      uni.$on('getPassengers', ({ data }) => {
        console.log('🎫 接收到乘客数据事件:', data)

        // 多重安全检查
        const passengers = Array.isArray(data) ? data : (data && Array.isArray(data.passengers) ? data.passengers : [])
        console.log('✅ 处理后的乘客数据:', passengers, '长度:', passengers.length)

        // 安全设置乘客数据
        if (Array.isArray(passengers) && passengers.length > 0) {
          // 验证每个乘客对象的完整性
          const safePassengers = passengers.filter(p => p && typeof p === 'object')
          this.orderForm.passengers = safePassengers
          console.log('✅ 设置orderForm.passengers，长度:', safePassengers.length)
        }
        else {
          this.orderForm.passengers = []
          console.log('⚠️ 乘客数据为空，设置为空数组')
        }

        // 安全更新显示列表
        this.updateShowList()
      })

      // 检查是否为往返程订单
      if (params.data) {
        this.handleFlightData(params.data)
      }
      else {
        // 兼容原有单程逻辑
        this.handleSingleTripData()
      }
    }
    catch (error) {
      console.error('❌ onLoad阶段发生错误:', error)
      this.handleLoadError(error)
    }
  },
  beforeUnmount() {
    // 移除事件监听，释放性能
    uni.$off('getPassengers')
  },
  onShow() {
    // 只有在有编辑乘客ID的情况下才调用更新，避免重复查询
    const editId = uni.getStorageSync('edit_passenger_id')
    if (editId) {
      console.log('📝 检测到编辑乘客ID，执行更新操作')
      this.updateOrConfirmUser()
    }
    else {
      console.log('📄 onShow: 无编辑操作，跳过更新')
    }
  },
  methods: {
    // 计算单个航班价格
    calculateFlightPrice(flight) {
      if (!flight)
        return 0

      // 价格计算调试（简化版）
      if (!flight.priceAdult && !flight.adultOrigPrice && !flight.taxFeeAdult)
        console.log('💰 价格计算调试 - 缺少价格数据:', flight)

      let price = 0
      const passengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []

      if (passengers.length === 0) {
        // 如果没有乘客，按照1个成人计算（用于显示基础价格）
        // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
        price = (Number(flight.priceAdult || flight.adultOrigPrice) || 0) + (Number(flight.taxFeeAdult) || 0)
      }
      else {
        passengers.forEach((item) => {
          if (item && item.personType === PERSON_TYPE.ADULT)
            price += (Number(flight.priceAdult || flight.adultOrigPrice) || 0) + (Number(flight.taxFeeAdult) || 0)
          else if (item && item.personType === PERSON_TYPE.CHILD)
            price += (Number(flight.priceChild) || 0) + (Number(flight.taxFeeChild) || 0)
        })
      }
      return price
    },
    // 计算往返程总价（兼容方法，包含所有费用）
    calculateRoundTripPrice() {
      const departurePrice = this.calculateFlightPrice(this.orderForm.departure)
      const returnPrice = this.calculateFlightPrice(this.orderForm.return)
      return departurePrice + returnPrice
    },

    // 计算往返程票价含税总价（仅票价+税费，不含机建燃油）
    calculateRoundTripTicketPriceOnly() {
      const departureTicketPrice = this.calculateFlightTicketPrice(this.orderForm.departure)
      const returnTicketPrice = this.calculateFlightTicketPrice(this.orderForm.return)
      return departureTicketPrice + returnTicketPrice
    },

    // 计算单个航班的票价（含税费）
    calculateFlightTicketPrice(flight) {
      if (!flight) {
        console.log('⚠️ calculateFlightTicketPrice: flight为空')
        return 0
      }

      console.log('💰 calculateFlightTicketPrice - 输入航班数据:', flight)
      console.log('   - priceAdult:', flight.priceAdult)
      console.log('   - adultOrigPrice:', flight.adultOrigPrice)
      console.log('   - taxFeeAdult:', flight.taxFeeAdult)

      let price = 0
      const passengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []

      if (passengers.length === 0) {
        // 如果没有乘客，按照1个成人计算：票价 + 税费
        const ticketPrice = Number(flight.priceAdult || flight.adultOrigPrice) || 0
        const taxFee = Number(flight.taxFeeAdult) || 0
        price = ticketPrice + taxFee
        console.log('   - 无乘客模式：ticketPrice=', ticketPrice, 'taxFee=', taxFee, 'total=', price)
      }
      else {
        passengers.forEach((item) => {
          if (item && item.personType === PERSON_TYPE.ADULT) {
            const ticketPrice = Number(flight.priceAdult || flight.adultOrigPrice) || 0
            const taxFee = Number(flight.taxFeeAdult) || 0
            price += ticketPrice + taxFee
          }
          else if (item && item.personType === PERSON_TYPE.CHILD) {
            const ticketPrice = Number(flight.priceChild) || 0
            const taxFee = Number(flight.taxFeeChild) || 0
            price += ticketPrice + taxFee
          }
        })
        console.log('   - 有乘客模式：passengers=', passengers.length, 'total=', price)
      }
      return price
    },

    // 计算单个航班的机建燃油费
    calculateFlightAirportAndFuel(flight) {
      if (!flight) {
        console.log('⚠️ calculateFlightAirportAndFuel: flight为空')
        return 0
      }

      console.log('🛢️ calculateFlightAirportAndFuel - 输入航班数据:', flight)
      console.log('   - oilFeeAdult:', flight.oilFeeAdult)
      console.log('   - amtAdultAirPortFee:', flight.amtAdultAirPortFee)

      let airportAndFuel = 0
      const passengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []

      if (passengers.length === 0) {
        // 如果没有乘客，按照1个成人计算：燃油费 + 机建费
        const oilFee = Number(flight.oilFeeAdult) || 0
        const airportFee = Number(flight.amtAdultAirPortFee) || 0
        airportAndFuel = oilFee + airportFee
        console.log('   - 无乘客模式：oilFee=', oilFee, 'airportFee=', airportFee, 'total=', airportAndFuel)
      }
      else {
        passengers.forEach((item) => {
          if (item && item.personType === PERSON_TYPE.ADULT) {
            airportAndFuel += (Number(flight.oilFeeAdult) || 0)
                             + (Number(flight.amtAdultAirPortFee) || 0)
          }
          else if (item && item.personType === PERSON_TYPE.CHILD) {
            airportAndFuel += (Number(flight.oilFeeChild) || 0)
                             + (Number(flight.amtChildAirPortFee) || 0)
          }
        })
        console.log('   - 有乘客模式：passengers=', passengers.length, 'total=', airportAndFuel)
      }
      return airportAndFuel
    },
    changeUser(val) {
      // 安全处理乘客数组操作
      const safePassengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []
      const isActive = safePassengers.findIndex(item => item && item.id === val.id) >= 0

      if (isActive) {
        this.orderForm.passengers = safePassengers.filter(item => item && item.id !== val.id)
      }
      else {
        const currentFlight = this.orderForm.isRoundTrip ? this.orderForm.departure : this.orderForm.flight
        if (val.personType === PERSON_TYPE.CHILD && !Number(currentFlight && currentFlight.peopleType && currentFlight.peopleType.child))
          return this.$showToast('暂不支持儿童，请在首页选择儿童后重新下单')

        // 确保passengers数组存在
        if (!Array.isArray(this.orderForm.passengers))
          this.orderForm.passengers = []

        this.orderForm.passengers.push(val)
      }
    },
    morePassengers() {
      const flightData = this.orderForm.isRoundTrip ? this.orderForm.departure : this.orderForm.flight
      uni.navigateTo({
        url: `/pages_common/passengers/index?data=${JSON.stringify(flightData)}`,
      })
    },
    operationUser(type, row) {
      this.operationUserType = type
      if (type === 'edit') {
        uni.navigateTo({
          url: `/pages_common/passengers/update?type=${type}&row=${JSON.stringify(row)}`,
        })
      }
      else {
        const currentFlight = this.orderForm.isRoundTrip ? this.orderForm.departure : this.orderForm.flight
        uni.navigateTo({
          url: `/pages_common/passengers/more?passengers=${JSON.stringify(this.orderForm.passengers)}&childNum=${currentFlight && currentFlight.peopleType && currentFlight.peopleType.child}`,
        })
      }
    },
    submit() {
      if (!this.orderForm.contact.contactName)
        return this.$showToast('请输入联系人姓名')
      if (!(/^(?:[\u4E00-\u9FA5·]{2,16})$/.test(this.orderForm.contact.contactName)))
        return this.$showToast('请输入正确的联系人姓名')
      if (!uni.$u.test.mobile(this.orderForm.contact.contactMobile))
        return this.$showToast('请输入正确的手机号')
      if (!this.orderForm.passengers.length)
        return this.$showToast('请选择乘机人')
      if (this.getFlightAirCode() === 'AIR_SC_NDC2C') {
        const currentFlight = this.orderForm.isRoundTrip ? this.orderForm.departure : this.orderForm.flight
        if (!(currentFlight && currentFlight.peopleType && currentFlight.peopleType.adult)) {
          // TODO: 本期不考虑儿童
          if (this.passengersAudlt > 1)
            return this.$showToast('请选择1名成人')
        }
        else {
          if (Number(this.passengersAudlt) !== Number(currentFlight && currentFlight.peopleType && currentFlight.peopleType.adult))
            return this.$showToast(`请选择${currentFlight && currentFlight.peopleType && currentFlight.peopleType.adult}名成人`)
          if (Number(this.passengersChild) !== Number(currentFlight?.peopleType?.child))
            return this.$showToast(`请选择${currentFlight?.peopleType?.child}名儿童`)
        }
      }
      if (this.passengersChild > 0 && this.passengersAudlt === 0)
        return this.$showToast('乘机人请添加成人')
      if (this.passengersChild > this.passengersAudlt)
        return this.$showToast('乘机人成人数量少于儿童数量，请添加成人')

      // 安全处理展开运算符
      const safeOrderForm = (this.orderForm && typeof this.orderForm === 'object') ? this.orderForm : {}
      const subData = {
        ...safeOrderForm,
        passengers: (this.orderForm.passengers || []).map((item) => {
          const sex = this.$getIdCardDate(item.certNum).sex
          // 安全处理乘客数据展开
          const safeItem = (item && typeof item === 'object') ? item : {}
          return {
            ...safeItem,
            // certNum: this.$encrypt.encrypt(item.certNum),
            // TODO: 拼写错误！历史遗留没法改了只能按他的来
            cretNum: (item.certNum),
            // tel: this.$encrypt.encrypt(item.tel),
            tel: (item.phone),
            sex,
            sexChina: sex,
            passengerType: item.personType,
          }
        }),
        contact: {
          // 安全处理联系人数据展开
          ...(this.orderForm.contact && typeof this.orderForm.contact === 'object' ? this.orderForm.contact : {}),
          // contactMobile: this.$encrypt.encrypt(this.orderForm.contact.contactMobile),
          contactMobile: (this.orderForm.contact.contactMobile),
        },
        ticketTotalAmount: this.ticketTotalAmount,
        // 往返程相关字段
        isRoundTrip: this.orderForm.isRoundTrip,
        journeyType: this.orderForm.isRoundTrip ? 'RT' : 'OW',
      }

      // 如果是往返程，添加去程回程信息
      if (this.orderForm.isRoundTrip) {
        // 修复往返程航班时间格式问题：确保 deptTime 和 arrTime 包含完整的日期时间
        const departure = { ...this.orderForm.departure }
        const returnFlight = { ...this.orderForm.return }

        // 处理去程航班时间格式
        if (departure.deptTime && departure.deptDate) {
          // 如果 deptTime 只包含时间部分，则与 deptDate 组合成完整日期时间
          if (departure.deptTime.length <= 5) { // 格式如 "09:15"
            departure.deptTime = `${departure.deptDate} ${departure.deptTime}:00`
          }
        }
        if (departure.arrTime && departure.deptDate) {
          // 处理到达时间，如果只包含时间部分，需要判断是否跨日
          if (departure.arrTime.length <= 5) { // 格式如 "11:30"
            // 简单处理：使用起飞日期作为到达日期（大部分国内航班不跨日）
            // 如果需要更精确的跨日判断，可以比较起飞时间和到达时间
            departure.arrTime = `${departure.deptDate} ${departure.arrTime}:00`
          }
        }

        // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
        if (departure.flightBaseInfoVo) {
          if (departure.flightBaseInfoVo.deptTime && departure.deptDate) {
            if (departure.flightBaseInfoVo.deptTime.length <= 5) {
              departure.flightBaseInfoVo.deptTime = `${departure.deptDate} ${departure.flightBaseInfoVo.deptTime}:00`
            }
          }
          if (departure.flightBaseInfoVo.arrTime && departure.deptDate) {
            if (departure.flightBaseInfoVo.arrTime.length <= 5) {
              departure.flightBaseInfoVo.arrTime = `${departure.deptDate} ${departure.flightBaseInfoVo.arrTime}:00`
            }
          }
        }

        // 处理回程航班时间格式
        if (returnFlight.deptTime && returnFlight.deptDate) {
          if (returnFlight.deptTime.length <= 5) { // 格式如 "14:20"
            returnFlight.deptTime = `${returnFlight.deptDate} ${returnFlight.deptTime}:00`
          }
        }
        if (returnFlight.arrTime && returnFlight.deptDate) {
          if (returnFlight.arrTime.length <= 5) { // 格式如 "16:45"
            returnFlight.arrTime = `${returnFlight.deptDate} ${returnFlight.arrTime}:00`
          }
        }

        // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
        if (returnFlight.flightBaseInfoVo) {
          if (returnFlight.flightBaseInfoVo.deptTime && returnFlight.deptDate) {
            if (returnFlight.flightBaseInfoVo.deptTime.length <= 5) {
              returnFlight.flightBaseInfoVo.deptTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.deptTime}:00`
            }
          }
          if (returnFlight.flightBaseInfoVo.arrTime && returnFlight.deptDate) {
            if (returnFlight.flightBaseInfoVo.arrTime.length <= 5) {
              returnFlight.flightBaseInfoVo.arrTime = `${returnFlight.deptDate} ${returnFlight.flightBaseInfoVo.arrTime}:00`
            }
          }
        }

        subData.departure = departure
        subData.return = returnFlight
        subData.departurePrice = this.departurePrice
        subData.returnPrice = this.returnPrice

        console.log('✅ 修复后的去程时间格式:', {
          deptTime: departure.deptTime,
          arrTime: departure.arrTime
        })
        console.log('✅ 修复后的回程时间格式:', {
          deptTime: returnFlight.deptTime,
          arrTime: returnFlight.arrTime
        })
      } else {
        // 单程航班也需要修复时间格式
        const flight = { ...this.orderForm.flight }
        if (flight.deptTime && flight.deptDate) {
          if (flight.deptTime.length <= 5) {
            flight.deptTime = `${flight.deptDate} ${flight.deptTime}:00`
          }
        }
        if (flight.arrTime && flight.deptDate) {
          if (flight.arrTime.length <= 5) {
            flight.arrTime = `${flight.deptDate} ${flight.arrTime}:00`
          }
        }

        // 同时修复嵌套的 flightBaseInfoVo 对象中的时间格式
        if (flight.flightBaseInfoVo) {
          if (flight.flightBaseInfoVo.deptTime && flight.deptDate) {
            if (flight.flightBaseInfoVo.deptTime.length <= 5) {
              flight.flightBaseInfoVo.deptTime = `${flight.deptDate} ${flight.flightBaseInfoVo.deptTime}:00`
            }
          }
          if (flight.flightBaseInfoVo.arrTime && flight.deptDate) {
            if (flight.flightBaseInfoVo.arrTime.length <= 5) {
              flight.flightBaseInfoVo.arrTime = `${flight.deptDate} ${flight.flightBaseInfoVo.arrTime}:00`
            }
          }
        }

        subData.flight = flight

        console.log('✅ 修复后的单程时间格式:', {
          deptTime: flight.deptTime,
          arrTime: flight.arrTime,
          flightBaseInfoVo: flight.flightBaseInfoVo
        })
      }

      this.showOrderInfo = true
      this.payBtnLoading = true
      setTimeout(() => {
        // 安全处理API参数展开
        const safeSubData = (subData && typeof subData === 'object') ? subData : {}
        this.$Api.orderReservation({
          ...safeSubData,
        }).then((res) => {
          if (res.returnCode === '000000') {
            uni.navigateTo({
              url: `/pages/order/detail?orderId=${res.data.orderId}`,
            })
          }
          else {
            this.errorContent = res.returnMessage
            this.showErrorModal = true
          }
        }).finally(() => {
          this.payBtnLoading = false
          this.showOrderInfo = false
        })
      }, 300)
    },
    confirmErrorModalFun() {
      this.showErrorModal = false
      setTimeout(() => {
        this.errorContent = ''
      }, 300)
    },
    async updateOrConfirmUser() {
      console.log('🔄 updateOrConfirmUser 开始执行')

      const res = await this.queryAllCommonPassenger()
      const id = uni.getStorageSync('edit_passenger_id')

      console.log('📝 编辑乘客ID:', id)
      console.log('📦 查询返回结果长度:', Array.isArray(res) ? res.length : 0)
      console.log('👥 当前userList长度:', this.userList.length)

      if (id) {
        // 编辑乘客的情况：更新显示列表中的对应项
        console.log('✏️ 处理编辑乘客逻辑')
        let save = null
        for (let i = 0; i < this.userList.length; i++) {
          if (this.userList[i].id === id) {
            // 安全处理用户数据展开
            const safeUser = (this.userList[i] && typeof this.userList[i] === 'object') ? this.userList[i] : {}
            save = { ...safeUser }
            break
          }
        }

        if (save) {
          // 更新showList中对应的项
          const safeShowList = Array.isArray(this.showList) ? this.showList : []
          safeShowList.forEach((item, index) => {
            if (item && item.id === id)
              this.$set(this.showList, index, save)
          })

          // 更新passengers中对应的项
          const safePassengers = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []
          safePassengers.forEach((item, index) => {
            if (item && item.id === id)
              this.$set(this.orderForm.passengers, index, save)
          })
          console.log('✅ 编辑乘客信息已更新')
        }

        // 清理编辑状态
        uni.setStorageSync('edit_passenger_id', '')
        console.log('🧹 已清理编辑状态')
      }
      else {
        // 新增乘客或初始加载的情况
        console.log('➕ 处理新增乘客或初始加载逻辑')

        // queryAllCommonPassenger() 已经正确设置了 userList，使用统一的更新方法
        this.updateShowList()
        console.log('✅ 使用统一方法更新showList，长度:', this.showList.length)
      }

      console.log('🎯 updateOrConfirmUser 执行完成')
      console.log('   - userList长度:', this.userList.length)
      console.log('   - showList长度:', this.showList.length)
      console.log('   - 乘客添加面板是否显示:', this.userList.length === 0)
    },
    confirmCountryPicker({ value }) {
      this.orderForm.phoneType = value[0].value
      this.showCountryPicker = false
    },
    toAgreement(page, name) {
      uni.navigateTo({
        url: `/pages_common/agreement/agreement?fileName=${page}&name=${name}`,
      })
    },
    async queryAllCommonPassenger() {
      try {
        if (!this.userList.length)
          this.loading = true

        console.log('🚀 开始查询常用乘客列表')

        // 从用户信息中获取memberId
        const userInfo = this.$store.state.userInfo || uni.getStorageSync('userInfo') || {}

        console.log('👤 完整用户信息结构:', userInfo)
        console.log('🔍 用户信息字段检查:')
        console.log('   - userInfo.id:', userInfo.id)
        console.log('   - userInfo.memberId:', userInfo.memberId)
        console.log('   - userInfo.userId:', userInfo.userId)
        console.log('   - userInfo.openId:', userInfo.openId)
        console.log('   - userInfo.token:', userInfo.token)
        console.log('   - userInfo.phone:', userInfo.phone)
        console.log('   - userInfo.nickName:', userInfo.nickName)
        console.log('   - 所有键名:', Object.keys(userInfo))

        // 扩展 memberId 获取逻辑，尝试更多可能的字段
        const memberId = userInfo.id
                         || userInfo.memberId
                         || userInfo.userId
                         || userInfo.openId
                         || userInfo.memberid // 小写
                         || userInfo.userid // 小写
                         || userInfo.openid // 小写
                         || userInfo.phone // 使用手机号作为兜底
                         || userInfo.token // 使用token作为最后兜底

        console.log('🆔 最终获取的memberId:', memberId)
        console.log('🆔 memberId类型:', typeof memberId)

        if (!memberId) {
          console.error('❌ 缺少memberId，但不跳转登录页面，允许用户继续添加乘客')

          // 设置空的用户列表，让页面显示创建乘客面板
          this.userList = []
          this.showList = []
          this.loading = false

          console.log('🔧 memberID缺失处理后状态:')
          console.log('   - userList长度:', this.userList.length)
          console.log('   - loading状态:', this.loading)
          console.log('   - showList长度:', this.showList.length)

          return []
        }

        console.log('📡 正在调用API queryAllCommonPassenger，参数:', { memberId })
        const res = await this.$Api.queryAllCommonPassenger({ memberId })
        console.log('📦 乘客接口返回结果:')
        console.log('   - 完整响应:', res)
        console.log('   - returnCode:', res?.returnCode)
        console.log('   - data类型:', typeof res?.data)
        console.log('   - data是否为数组:', Array.isArray(res?.data))
        console.log('   - data内容:', res?.data)

        // 多重安全检查 - 处理API成功和失败的情况
        let list = []
        if (res && res.returnCode === '000000') {
          // API调用成功
          list = Array.isArray(res.data) ? res.data : []
          console.log('✅ API调用成功，提取乘客列表')
        }
        else if (Array.isArray(res)) {
          // 直接返回数组的情况
          list = res
          console.log('✅ API直接返回数组')
        }
        else {
          // API调用失败或返回格式异常
          console.warn('⚠️ API调用失败或返回格式异常:')
          console.warn('   - 响应:', res)
          console.warn('   - returnCode:', res?.returnCode)
          console.warn('   - returnMessage:', res?.returnMessage)
          list = []
        }
        console.log('🔍 最终处理后的乘客列表:', list, '长度:', list.length)

        if (list && Array.isArray(list) && list.length > 0) {
          // 验证每个乘客对象的完整性
          const safeList = list.filter(passenger => passenger && typeof passenger === 'object')
          this.userList = [...safeList] // 安全的扩展运算符使用
          console.log('✅ 安全设置userList，长度:', this.userList.length)
        }
        else {
          this.userList = []
          console.log('⚠️ 乘客列表为空，设置为空数组')
        }

        // 更新显示列表，不再依赖isFirst标志
        this.updateShowList()

        if (this.isFirst) {
          this.isFirst = false
          console.log('✅ 首次加载完成，showList长度:', this.showList.length)
        }

        this.loading = false
        return Array.isArray(list) ? list : []
      }
      catch (error) {
        console.error('查询乘客列表失败:', error)
        // 确保所有相关数组都被安全初始化
        this.userList = []
        this.showList = []
        if (this.orderForm && !Array.isArray(this.orderForm.passengers))
          this.orderForm.passengers = []

        this.loading = false
        return [] // 确保异常时也返回空数组
      }
    },
    queryAgreementList(airCode) {
      this.$Api.queryAgreementList({ airCode }).then((res) => {
        const list = res || []
        if (list && list.length)
          this.agreementList = list
      }).catch(() => {
        this.agreementList = []
      })
    },
    changeAgree(val) {
      this.orderForm.isAgree = val
    },
    onAgree(val) {
      this.$set(this.orderForm, 'isAgree', val)
    },
    filterPassengers() {
      const userList = this.userList || []
      const passengers = this.orderForm.passengers || []
      const arr = userList
        .map((item) => {
          for (let i = 0; i < passengers.length; i++) {
            if (item.id === passengers[i].id)
              return null
          }
          return item
        })
        .filter(item => item !== null)
      return arr
    },
    // 获取当前模式下的航班代码
    getFlightAirCode() {
      if (this.orderForm.isRoundTrip) {
        // 往返程模式使用去程航班的航司代码
        return this.orderForm.departure?.airCode || ''
      }
      else {
        // 单程模式
        return this.orderForm.flight?.airCode || ''
      }
    },
    // 获取当前模式下的乘客类型信息
    getFlightPeopleType() {
      if (this.orderForm.isRoundTrip) {
        // 往返程模式使用去程航班的乘客类型
        return this.orderForm.departure?.peopleType?.adult || false
      }
      else {
        // 单程模式
        return this.orderForm.flight?.peopleType?.adult || false
      }
    },
    // 获取订单确认页面显示的航线信息
    getOrderDisplayRoute() {
      if (this.orderForm.isRoundTrip) {
        const departure = this.orderForm.departure
        const returnFlight = this.orderForm.return
        if (departure && returnFlight)
          return `${departure.deptCityName || ''} ⇄ ${departure.arrCityName || ''}`

        return '往返程'
      }
      else {
        const flight = this.orderForm.flight
        return `${(flight && flight.deptCityName) ? `${flight.deptCityName} - ` : ''}${flight?.arrCityName || ''}`
      }
    },
    // 获取订单确认页面显示的日期信息
    getOrderDisplayDate() {
      if (this.orderForm.isRoundTrip) {
        const departure = this.orderForm.departure
        const returnFlight = this.orderForm.return
        if (departure && returnFlight && departure.deptTime && returnFlight.deptTime) {
          const deptDate = this.$formatDateStr(departure.deptTime, 'mm月dd日')
          const returnDate = this.$formatDateStr(returnFlight.deptTime, 'mm月dd日')
          return `${deptDate} - ${returnDate}`
        }
        return '往返程日期'
      }
      else {
        const flight = this.orderForm.flight
        return (flight && flight.deptTime) ? this.$formatDateStr(flight.deptTime, 'yyyy年mm月dd日 WW') : ''
      }
    },
    // 获取订单确认页面显示的航班信息
    getOrderDisplayFlight() {
      if (this.orderForm.isRoundTrip) {
        const departure = this.orderForm.departure
        if (departure && departure.flightNo && departure.deptTime)
          return `${departure.flightNo} ${this.$formatDateStr(departure.deptTime, 'hh:MM')}起飞`

        return '往返程航班'
      }
      else {
        const flight = this.orderForm.flight
        return `${(flight && flight.flightNo) || ''} ${(flight && flight.deptTime) ? this.$formatDateStr(flight.deptTime, 'hh:MM') : ''}起飞`
      }
    },
    // 新增：初始化乘客数据的方法
    async initializePassengerData() {
      try {
        console.log('🚀 开始初始化乘客数据')
        console.log('🔧 调用前状态检查:')
        console.log('   - userList长度:', this.userList.length)
        console.log('   - loading状态:', this.loading)
        console.log('   - showList长度:', this.showList.length)

        await this.queryAllCommonPassenger()

        console.log('✅ 乘客数据初始化完成')
        console.log('🔧 调用后状态检查:')
        console.log('   - userList长度:', this.userList.length)
        console.log('   - loading状态:', this.loading)
        console.log('   - showList长度:', this.showList.length)
        console.log('   - userList内容:', this.userList)
      }
      catch (error) {
        console.error('❌ 乘客数据初始化失败:', error)
        // 确保在失败情况下也有合理的初始状态
        this.userList = []
        this.showList = []
        this.loading = false
        console.log('🔧 错误处理后状态:')
        console.log('   - userList长度:', this.userList.length)
        console.log('   - loading状态:', this.loading)
        console.log('   - showList长度:', this.showList.length)
      }
    },

    // 新增：处理航班数据的方法
    handleFlightData(data) {
      try {
        const orderData = JSON.parse(decodeURIComponent(data))
        console.log('📦 create页面接收到的数据:', orderData)
        console.log('🔍 orderData.isRoundTrip:', orderData.isRoundTrip)
        console.log('🔍 orderData.departure:', orderData.departure)
        console.log('🔍 orderData.return:', orderData.return)

        this.orderForm.isRoundTrip = orderData.isRoundTrip || false

        console.log('✅ 设置 this.orderForm.isRoundTrip =', this.orderForm.isRoundTrip)

        if (this.orderForm.isRoundTrip)
          this.handleRoundTripData(orderData)
        else
          this.handleSingleTripData()
      }
      catch (error) {
        console.error('❌ 处理航班数据失败:', error)
        this.handleLoadError(error)
      }
    },

    // 新增：处理往返程数据的方法
    handleRoundTripData(orderData) {
      try {
        // 安全处理往返程数据
        this.orderForm.departure = orderData.departure || {}
        this.orderForm.return = orderData.return || {}
        this.orderForm.totalPrice = orderData.totalPrice || 0
        this.orderForm.passengers = orderData.passengers || []

        console.log('✅ 往返程数据安全赋值完成:')
        console.log('   - departure:', this.orderForm.departure)
        console.log('   - return:', this.orderForm.return)
        console.log('   - passengers:', this.orderForm.passengers)

        // 详细字段检查
        console.log('🔍 create.vue 详细数据检查:')
        console.log('   - isRoundTrip:', this.orderForm.isRoundTrip)
        console.log('   - departure存在:', !!this.orderForm.departure)
        console.log('   - return存在:', !!this.orderForm.return)

        if (this.orderForm.departure) {
          console.log('   📄 去程详细信息:')
          console.log('     - flightNo:', this.orderForm.departure.flightNo)
          console.log('     - airName:', this.orderForm.departure.airName)
          console.log('     - deptTime:', this.orderForm.departure.deptTime)
          console.log('     - arrTime:', this.orderForm.departure.arrTime)
          console.log('     - deptAirportName:', this.orderForm.departure.deptAirportName)
          console.log('     - arrAirportName:', this.orderForm.departure.arrAirportName)
          console.log('     - cabinClsName:', this.orderForm.departure.cabinClsName)
          console.log('     - priceAdult:', this.orderForm.departure.priceAdult || this.orderForm.departure.adultOrigPrice)
          console.log('     - 完整对象:', this.orderForm.departure)
        }

        if (this.orderForm.return) {
          console.log('   📄 回程详细信息:')
          console.log('     - flightNo:', this.orderForm.return.flightNo)
          console.log('     - airName:', this.orderForm.return.airName)
          console.log('     - deptTime:', this.orderForm.return.deptTime)
          console.log('     - arrTime:', this.orderForm.return.arrTime)
          console.log('     - deptAirportName:', this.orderForm.return.deptAirportName)
          console.log('     - arrAirportName:', this.orderForm.return.arrAirportName)
          console.log('     - cabinClsName:', this.orderForm.return.cabinClsName)
          console.log('     - priceAdult:', this.orderForm.return.priceAdult || this.orderForm.return.adultOrigPrice)
          console.log('     - 完整对象:', this.orderForm.return)
        }

        // 使用去程航班的航司代码查询协议
        const departure = this.orderForm.departure || {}
        const flightBaseInfo = departure.flightBaseInfoVo || {}
        let airCode = departure.airCode || flightBaseInfo.airCode || ''

        // 往返航班南航使用NDC2C
        if (airCode === 'CZ')
          airCode = 'AIR_CZ_NDC2C'

        console.log('🔍 提取的航司代码:', departure.airCode || flightBaseInfo.airCode || '', '-> 协议代码:', airCode)
        this.queryAgreementList(airCode)

        // 往返程模式下，确保showList得到正确更新
        this.updateShowList()
      }
      catch (error) {
        console.error('❌ 处理往返程数据失败:', error)
        throw error // 重新抛出错误，让上级处理
      }
    },

    // 新增：处理单程数据的方法
    handleSingleTripData() {
      try {
        const data = uni.getStorageSync('create_flight')
        this.orderForm.flight = data || {}
        this.queryAgreementList(this.orderForm.flight.airCode || '')

        // 单程模式下，确保showList得到正确更新
        this.updateShowList()
      }
      catch (error) {
        console.error('❌ 处理单程数据失败:', error)
        throw error
      }
    },

    // 新增：更新显示列表的方法
    updateShowList() {
      try {
        const passengersList = Array.isArray(this.orderForm.passengers) ? this.orderForm.passengers : []
        const userList = Array.isArray(this.userList) ? this.userList : []

        console.log('🔄 更新显示列表: 已选乘客', passengersList.length, '常用乘客', userList.length)

        if (passengersList.length > 0)
          this.showList = [...passengersList]
        else if (userList.length > 0)
          this.showList = userList.slice(0, 2)
        else
          this.showList = []

        console.log('✅ showList更新完成，长度:', this.showList.length,
          '显示模式:', this.userList.length === 0 ? 'create-panel' : 'passenger-list')
      }
      catch (error) {
        console.error('❌ 更新showList失败:', error)
        this.showList = []
      }
    },

    // 新增：处理加载错误的方法
    handleLoadError(error) {
      uni.showModal({
        title: '页面初始化失败',
        content: `错误信息：${error.message}`,
        showCancel: true,
        confirmText: '返回上一页',
        cancelText: '继续',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack()
          }
          else {
            // 用户选择继续，尝试使用localStorage中的数据
            this.handleSingleTripData()
          }
        },
      })
    },
  },
}
</script>

<template>
  <view class="page-wrap">
    <view class="page-content">
      <view class="page-content-header" :class="{ 'round-trip-mode': orderForm.isRoundTrip }">
        <!-- 单程航班信息 -->
        <view v-if="!orderForm.isRoundTrip" class="single-trip-info">
          <order-panel :row-data="orderForm.flight" :show-header="false" show-other-info />
        </view>

        <!-- 往返程航班信息 -->
        <view v-if="orderForm.isRoundTrip" class="round-trip-info">
          <!-- 去程信息 -->
          <view class="flight-segment-compact">
            <order-panel :row-data="departureData" :show-header="false" show-other-info />
          </view>

          <!-- 回程信息 -->
          <view class="flight-segment-compact">
            <order-panel :row-data="returnData" :show-header="false" show-other-info />
          </view>
        </view>
      </view>
      <view class="page-content-box" :class="{ 'round-trip-mode': orderForm.isRoundTrip }">
        <u-skeleton :loading="loading" :animate="true" rows="3" :title="false" rows-height="100" :rows-width="['100%', '100%', '100%']">
          <view v-if="userList.length === 0">
            <create-user-panel title="乘机人" :is-order-page="true" @on-confirm="updateOrConfirmUser" />
          </view>
          <view v-else class="cell-row" style="margin-top:0">
            <view v-if="getFlightAirCode() === 'AIR_SC_NDC2C' && getFlightPeopleType()" class="cell-row-top">
              <view class="cell-row-top-icon" />
              <view class="cell-row-top-infotitle">
                温馨提示:
              </view>
              <view class="cell-row-top-infotext">
                乘机人类型和数量请与首页选择一致
              </view>
              <view class="cell-row-top-right">
                <view class="cell-row-top-right-top" />
                <view class="cell-row-top-right-bottom" />
              </view>
            </view>
            <info-cell style="padding-bottom: 0;" :title="passengersNumString ? `已选：${passengersNumString}` : '乘机人'" :shanhang="getFlightAirCode() === 'AIR_SC_NDC2C' && getFlightPeopleType()" show-content>
              <template #value>
                <view class="value-box">
                  <view class="add-btn" @click="operationUser('add')">
                    <uni-icons type="plusempty" size="14" :color="primaryColor" />
                    <text>更多乘机人</text>
                  </view>
                </view>
              </template>
              <template #content>
                <view class="content-row">
                  <passenger-checkbox
                    v-for="(item, index) in (showList || [])" :key="index" :item="item" :need-line="index !== 0"
                    :passengers="orderForm.passengers || []" @on-check="changeUser"
                  />
                </view>
              </template>
            </info-cell>
          </view>
        </u-skeleton>
        <view v-if="userList.length && !loading" class="cell-row cell-group">
          <info-cell>
            <template #title>
              <view class="title-box">
                <view>联系人姓名</view>
              </view>
            </template>
            <template #value>
              <view class="cell-value">
                <u-input
                  v-model="orderForm.contact.contactName" placeholder="请输入" :border="false" :maxlength="20"
                  type="text" cursor-spacing="50"
                />
              </view>
            </template>
          </info-cell>
          <info-cell>
            <template #title>
              <view class="title-box">
                <view>联系电话</view>
              </view>
            </template>
            <template #value>
              <view class="cell-value">
                <text class="cell-value-phoneType">
                  {{ orderForm.contact.phoneType }}
                </text>
                <u-input
                  v-model="orderForm.contact.contactMobile" placeholder="请输入" :border="false" :maxlength="11"
                  type="number" cursor-spacing="50"
                />
              </view>
            </template>
          </info-cell>
          <info-cell>
            <template #title>
              <view class="title-box">
                <view>联系邮箱</view>
              </view>
            </template>
            <template #value>
              <view class="cell-value">
                <u-input
                  v-model="orderForm.contact.contactEmail" placeholder="请输入" :border="false" :maxlength="20"
                  type="text" cursor-spacing="50"
                />
              </view>
            </template>
          </info-cell>
        </view>
      </view>
    </view>
    <view class="tool-box" :class="{ down: showOperationUser }">
      <!-- TODO: 下面要加 v-if="agreementList.length" -->
      <view v-if="!showOrderDetail" class="agree-box">
        <agreement
          :active="agreementActive" :open-popup="true" jump-text="《购票须知》"
          :list="agreementList" @on-change-active="changeAgree" @close="closeAgreementPopup" @on-agree="onAgree"
        />
      </view>
      <view class="btn-box">
        <view class="order-info">
          <view class="money">
            <text>￥</text>
            <text>{{ ticketTotalAmount !== '--' ? $formatMoney(ticketTotalAmount) : '--' }}</text>
          </view>
          <view v-if="orderForm.passengers.length" class="order-people">
            已选{{ passengersNum }}人
          </view>
        </view>
        <view class="order-tool">
          <view v-if="orderForm.passengers.length" class="order-detail-btn" @click="showOrderDetail = !showOrderDetail">
            <text>明细</text>
            <uni-icons
              custom-prefix="iconfont"
              :type="`${showOrderDetail ? 'icon-xiangxia-shouqi' : 'icon-xiangshang-zhankai'}`" size="12"
              color="#999999" style="margin-left: 4px;"
            />
          </view>
          <view class="order-btn">
            <u-button
              type="primary" :disabled="disabled" :loading="payBtnLoading"
              :custom-style="{ width: '88px', height: '40px', marginLeft: '16px', borderRadius: '4px', fontWeight: 'bold' }"
              @click="submit"
            >
              提交订单
            </u-button>
          </view>
        </view>
      </view>
    </view>
    <u-popup
      :safe-area-inset-bottom="false" :show="showOrderDetail" :round="10" mode="bottom" z-index="996"
      :overlay-style="{ bottom: 'calc(53px + env(safe-area-inset-bottom))', zIndex: 990 }"
      :custom-style="{ bottom: 'calc(53px + env(safe-area-inset-bottom))' }"
      @close="showOrderDetail = false"
    >
      <view class="popup-wrap">
        <view class="popup-header">
          <u-icon name="close" color="#000" size="16" @click="showOrderDetail = false" />
          <view class="popup-header-title">
            价格明细
          </view>
        </view>
        <PriceDetail :passengers-audlt="passengersAudlt" :passengers-child="passengersChild" :order-form="orderForm" />
      </view>
    </u-popup>
    <u-popup
      mode="center" :show="showOrderInfo" :round="0" z-index="10101" :overlay-style="{ zIndex: 10100 }"
      :custom-style="{ background: 'transparent' }" :close-on-click-overlay="false"
    >
      <view class="order-info-content">
        <view class="info-box">
          <view class="info-title">
            请确认订单信息
          </view>
          <view class="info-row">
            <view class="info-item">
              <text>
                {{ getOrderDisplayRoute() }}
              </text>
              <view class="info-icon">
                <uni-icons
                  v-show="timeNumber > 1" custom-prefix="iconfont" type="icon-xuanze" size="16"
                  :color="primaryColor"
                />
              </view>
            </view>
            <view class="info-item">
              <text>
                {{ getOrderDisplayDate() }}
              </text>
              <uni-icons
                v-show="timeNumber > 3" custom-prefix="iconfont" type="icon-xuanze" size="16"
                :color="primaryColor"
              />
            </view>
            <view class="info-item">
              <text>
                {{ getOrderDisplayFlight() }}
              </text>
              <uni-icons
                v-show="timeNumber > 5" custom-prefix="iconfont" type="icon-xuanze" size="16"
                :color="primaryColor"
              />
            </view>
          </view>
          <view class="info-title">
            请确认订单信息
          </view>
          <view class="info-row">
            <view class="info-item">
              <text>{{ passagersStr }}</text>
              <uni-icons
                v-show="timeNumber > 7" custom-prefix="iconfont" type="icon-xuanze" size="16"
                :color="primaryColor"
              />
            </view>
          </view>
        </view>
        <view class="loading-panel">
          <u-loading-icon text="正在生成订单" size="18" text-size="14" color="#666666" text-color="#666666" />
        </view>
      </view>
    </u-popup>
    <u-picker
      :show="showCountryPicker" :columns="countryColumns" @cancel="showCountryPicker = false"
      @confirm="confirmCountryPicker"
    />
    <u-modal
      title="提示" :show="showErrorModal" width="260px" :close-on-click-overlay="false" :show-cancel-button="false"
      :confirm-color="primaryColor" :content="errorContent" @confirm="confirmErrorModalFun"
    />
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
.page-header {
  .blank-box {
    height: 25px;
  }
}

.page-content {
  width: 100%;
  min-height: 100vh;
  padding-bottom: calc(53px + env(safe-area-inset-bottom)); // 为底部按钮留空间
  background: #f5f5f5;

  .page-content-header {
    // 让航班信息成为文档流的一部分
    position: relative;
    min-height: auto;
    padding: 0; // 移除padding，让子组件自己控制
    background: transparent; // 透明背景，让子组件控制背景色
  }

  .page-content-box {
    // 让内容自然流动
    margin-top: 0;
    min-height: auto;
    padding: 8px;
    background: #f5f5f5;
  }

  .cell-row {
    margin-top: 6px;

    .title-box {
      display: flex;
      align-items: center;
      width: 88px;
      text {
        display: inline-block;
        width: 32px;
        margin-left: 10px;
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
        color: #666666;
      }
    }

    ::v-deep &.cell-group {
      overflow: hidden;
      border-radius: 8px;
      box-shadow: 0px 2px 6px 0px rgba(202, 202, 202, 0.42);

      .info-cell-box {
        border-radius: 0 !important;
      }

    }

    ::v-deep .cell-value {
      display: flex;
      align-items: center;
      width: calc(100% - 64px - 8px);
      z-index: 1 !important;

      .u-input {
        padding: 0 !important;
        z-index: 1 !important;
      }
    }
    .cell-value-phoneType {
      margin-right: 50rpx;
      display: inline-block;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        right: -27rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 2rpx;
        height: 32rpx;
        background: #D8D8D8;
        border-radius: 1rpx;
      }
    }

    .value-box {
      display: flex;
      justify-content: flex-end;
      width: 100%;

      .add-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 120px;
        height: 24px;
        font-weight: bold;
        background: rgba(132, 108, 245, 0.1);
        border-radius: 16px;
        color: $u-primary;

        text {
          margin-left: 4px;
        }
      }
    }

    &-top {
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      overflow: hidden;
      position: relative;
      display: flex;
      padding: 0 18rpx;
      justify-content: flex-start;
      align-items: center;
      height: 72rpx;
      background: linear-gradient(37deg, #FFFCF5 0%, #FFEFC3 100%), #FFFFFF;

      &-icon {
        margin-top: 12rpx;
        width: 44rpx;
        height: 46rpx;
        background: url('~@/static/img/light-icon.png') no-repeat;
        background-size: 100%;
      }

      &-infotitle {
        font-weight: 500;
        font-size: 28rpx;
        color: #FA9A14;
        line-height: 40rpx;
      }

      &-infotext {
        margin-left: 16rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FA9A14;
        line-height: 40rpx;
      }

      &-right {
        position: absolute;
        width: 48rpx;
        height: 66rpx;
        top: 6rpx;
        right: 0;

        &-top {
          position: absolute;
          right: 14rpx;
          width: 40rpx;
          height: 28rpx;
          background: url('~@/static/img/star-icon.png') no-repeat;
          background-size: 100%;

        }

        &-bottom {
          position: absolute;
          bottom: 0;
          width: 48rpx;
          height: 48rpx;
          background: url('~@/static/img/under-icon.png') no-repeat;
          background-size: 100%;

        }
      }
    }
  }

  ::v-deep .order-panel-wrap {
    .u-skeleton__wrapper__content__title {
      margin: 6px auto 0 !important;
    }

    .u-skeleton__wrapper__content__rows {
      margin: 12px auto 0 !important;
    }
  }
}

::v-deep .tool-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1000;

  &.down {
    z-index: 1;
  }

  .agree-box {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 8px 12px;
    //transition: all 0.5s ease-in;
    opacity: 1;
    pointer-events: all;

    &.hide {
      opacity: 0;
      pointer-events: none;
    }

    background: #F7F8FA;
  }

  .btn-box {
    height: 38px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 16px;
    background: #ffffff;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
    box-shadow: 0px -1px 0px 0px rgba(236, 233, 233, 0.5);
    box-sizing: content-box;

    .order-info {
      display: flex;
      align-items: center;

      .money {
        margin-top: -8rpx;
        color: $u-error;

        text:nth-child(2) {
          font-size: 20px;
          font-weight: bold;
        }
      }

      .order-people {
        margin-left: 8rpx;
        font-size: 24rpx;
        color: #666666;
      }
    }

    .order-tool {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .order-detail-btn {
        display: flex;
        align-items: center;
        color: #666666;
        line-height: 20px;
      }
    }
  }
}
.popup-wrap{
  background: #F5F5F5;
  border-radius: 16px 16px 0px 0px;
  .popup-header{
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 16px;
    background: #F5F5F5;
    border-radius: 16px 16px 0px 0px;
    &-title{
      margin-left: -8px;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      flex: 1;
      text-align: center;
    }
  }
}

.order-info-content {
  width: 250px;
  background: #ffffff;
  border-radius: 3px;

  .info-box {
    position: relative;
    padding: 2px 16px 16px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      margin: 0;
      border-bottom-width: 1px;
      border-bottom-style: solid;
      transform: scaleY(0.5);
      border-color: #EFEFEF
    }

    .info-title {
      margin-top: 16px;
      font-size: 12px;
      color: #999999;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 6px;

    }
  }

  .loading-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
  }
}

.pay-info-box {
  .money {
    padding: 50px 0 30px;
    text-align: center;
    font-size: 24px;

    text:nth-child(2) {
      margin-left: 2px;
      font-size: 32px;
      font-family: DIN-Medium;

    }
  }

  .pay-cell-row {
    position: relative;
    padding-bottom: 50px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      margin: 0;
      border-top-width: 1px;
      border-top-style: solid;
      transform: scaleY(0.5);
      border-color: #EFEFEF
    }

    .pay-cell-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 20px;
      font-size: 16px;
      color: #000000;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        margin: 0;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        transform: scaleY(0.5);
        border-color: #EFEFEF
      }

      .pay-name {
        display: flex;
        align-items: center;

        text {
          margin-left: 15px;
        }
      }
    }
  }
}

.pay-btn-box {
  padding: 20px;

  .pay-btn-text {
    text:first-child {
      font-weight: bold;
      margin-right: 4px;
    }
  }
}

.popup-close {
  position: absolute;
  left: 10px;
  top: 10px;
  padding: 10px;
  z-index: 2;
}

// 往返程航班信息样式
.round-trip-info {

  .flight-segment-compact {
    margin-bottom: 4px; // 航班之间的间距

    // 最后一个航班添加底部间距
    &:last-child {
      margin-bottom: 12px;
    }

    // 航班信息恢复白色背景
    ::v-deep .order-panel-wrap {
      background: #ffffff; // 恢复白色背景
      border-radius: 8px;
      margin: 8px;
      box-shadow: 0px 2px 6px 0px rgba(202, 202, 202, 0.42);

      .order-panel-top {
        margin-bottom: 4px;
      }

      .order-panel-content {
        padding: 8px 0;
      }
    }
  }
}

/* 单程航班信息样式 */
.single-trip-info {
  // 单程航班信息背景
  ::v-deep .order-panel-wrap {
    background: #ffffff;
    border-radius: 8px;
    margin: 8px 8px 12px 8px; // 底部多一些间距，与乘客区域分隔
    box-shadow: 0px 2px 6px 0px rgba(202, 202, 202, 0.42);

    .order-panel-content {
      padding: 8px 0;
    }
  }
}
</style>

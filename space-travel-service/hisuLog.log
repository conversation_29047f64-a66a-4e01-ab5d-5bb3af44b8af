2025-07-29 22:35:17,147 [DEBUG] [DelayRecoverThread] ExtensionLoader -  [DUBBO] package com.alibaba.dubbo.rpc.cluster;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
public class Cluster$Adpative implements com.alibaba.dubbo.rpc.cluster.Cluster {
public com.alibaba.dubbo.rpc.Invoker join(com.alibaba.dubbo.rpc.cluster.Directory arg0) throws com.alibaba.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("com.alibaba.dubbo.rpc.cluster.Directory argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("com.alibaba.dubbo.rpc.cluster.Directory argument getUrl() == null");com.alibaba.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("cluster", "failover");
if(extName == null) throw new IllegalStateException("Fail to get extension(com.alibaba.dubbo.rpc.cluster.Cluster) name from url(" + url.toString() + ") use keys([cluster])");
com.alibaba.dubbo.rpc.cluster.Cluster extension = (com.alibaba.dubbo.rpc.cluster.Cluster)ExtensionLoader.getExtensionLoader(com.alibaba.dubbo.rpc.cluster.Cluster.class).getExtension(extName);
return extension.join(arg0);
}
}, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,164 [DEBUG] [DelayRecoverThread] ExtensionLoader -  [DUBBO] package com.alibaba.dubbo.registry;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
public class RegistryFactory$Adpative implements com.alibaba.dubbo.registry.RegistryFactory {
public com.alibaba.dubbo.registry.Registry getRegistry(com.alibaba.dubbo.common.URL arg0) {
if (arg0 == null) throw new IllegalArgumentException("url == null");
com.alibaba.dubbo.common.URL url = arg0;
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Fail to get extension(com.alibaba.dubbo.registry.RegistryFactory) name from url(" + url.toString() + ") use keys([protocol])");
com.alibaba.dubbo.registry.RegistryFactory extension = (com.alibaba.dubbo.registry.RegistryFactory)ExtensionLoader.getExtensionLoader(com.alibaba.dubbo.registry.RegistryFactory.class).getExtension(extName);
return extension.getRegistry(arg0);
}
}, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,185 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799716663, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,236 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799716663, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,283 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799716663, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799716663], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,290 [DEBUG] [DelayRecoverThread] ExtensionLoader -  [DUBBO] package com.alibaba.dubbo.rpc.cluster;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
public class RouterFactory$Adpative implements com.alibaba.dubbo.rpc.cluster.RouterFactory {
public com.alibaba.dubbo.rpc.cluster.Router getRouter(com.alibaba.dubbo.common.URL arg0) {
if (arg0 == null) throw new IllegalArgumentException("url == null");
com.alibaba.dubbo.common.URL url = arg0;
String extName = url.getProtocol();
if(extName == null) throw new IllegalStateException("Fail to get extension(com.alibaba.dubbo.rpc.cluster.RouterFactory) name from url(" + url.toString() + ") use keys([protocol])");
com.alibaba.dubbo.rpc.cluster.RouterFactory extension = (com.alibaba.dubbo.rpc.cluster.RouterFactory)ExtensionLoader.getExtensionLoader(com.alibaba.dubbo.rpc.cluster.RouterFactory.class).getExtension(extName);
return extension.getRouter(arg0);
}
}, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,297 [DEBUG] [DelayRecoverThread] ExtensionLoader -  [DUBBO] package com.alibaba.dubbo.rpc.cluster;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
public class ConfiguratorFactory$Adpative implements com.alibaba.dubbo.rpc.cluster.ConfiguratorFactory {
public com.alibaba.dubbo.rpc.cluster.Configurator getConfigurator(com.alibaba.dubbo.common.URL arg0) {
if (arg0 == null) throw new IllegalArgumentException("url == null");
com.alibaba.dubbo.common.URL url = arg0;
String extName = url.getProtocol();
if(extName == null) throw new IllegalStateException("Fail to get extension(com.alibaba.dubbo.rpc.cluster.ConfiguratorFactory) name from url(" + url.toString() + ") use keys([protocol])");
com.alibaba.dubbo.rpc.cluster.ConfiguratorFactory extension = (com.alibaba.dubbo.rpc.cluster.ConfiguratorFactory)ExtensionLoader.getExtensionLoader(com.alibaba.dubbo.rpc.cluster.ConfiguratorFactory.class).getExtension(extName);
return extension.getConfigurator(arg0);
}
}, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,315 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,315 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799717313, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,316 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799717315&pid=96734&server=servletx&side=provider&timestamp=1753799717313 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799716657, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,328 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799717313, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,367 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799717313, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,416 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799717313, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade&logger=log4j&methods=b2bQueryOrderDetail,queryOrderList,queryOrderAndRefundList,b2bQueryOrderList,queryOrderDetail&pid=96734&server=servletx&side=provider&timestamp=1753799717313], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,436 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,436 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717421, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,437 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799717437&pid=96734&server=servletx&side=provider&timestamp=1753799717421 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717421, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,446 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717421, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,488 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717421, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,535 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717421, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717421], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,536 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,537 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717536, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,537 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.order.OrderFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799717537&pid=96734&server=servletx&side=provider&timestamp=1753799717536 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717421, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,538 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717536, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,577 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717536, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,625 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717536, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.order.OrderFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.order.OrderFacade&logger=log4j&methods=paySuccess,createOrder,orderPay,order&pid=96734&server=servletx&side=provider&timestamp=1753799717536], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,640 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,640 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717629, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,640 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799717640&pid=96734&server=servletx&side=provider&timestamp=1753799717629 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717629, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,642 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717629, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,681 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717629, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,728 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717629, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717629], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,729 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,730 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,730 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717729, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,730 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,731 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.test.TestFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799717730&pid=96734&server=servletx&side=provider&timestamp=1753799717729 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717629, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,732 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,733 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,733 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717729, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,734 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,735 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,735 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,736 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,737 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,738 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,738 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,739 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,740 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,741 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,741 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,742 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,743 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,744 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,744 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,745 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,746 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,747 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,748 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,749 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,750 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,750 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,751 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,752 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,753 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,754 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,755 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,756 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,757 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,758 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,759 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,760 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,761 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,761 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,762 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,917 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717729, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:17,925 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,938 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:17,965 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717729, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.test.TestFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.test.TestFacade&logger=log4j&methods=test&pid=96734&server=servletx&side=provider&timestamp=1753799717729], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799717981, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718003&pid=96734&server=servletx&side=provider&timestamp=1753799717981 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717981, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,007 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799717981, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,046 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799717981, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,095 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799717981, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799717981], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,097 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,097 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799718096, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,098 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718097&pid=96734&server=servletx&side=provider&timestamp=1753799718096 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799717981, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,100 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799718096, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,139 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799718096, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,185 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799718096, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.ReadFileFacade&logger=log4j&methods=readCityAndAirportFileByString,readCityAndAirportFile&pid=96734&server=servletx&side=provider&timestamp=1753799718096], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,205 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,205 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718191, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,206 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718205&pid=96734&server=servletx&side=provider&timestamp=1753799718191 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718191, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,208 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718191, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,246 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718191, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,294 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718191, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718191], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,299 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,299 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718297, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,299 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.file.AgreementFile url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718299&pid=96734&server=servletx&side=provider&timestamp=1753799718297 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718191, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,303 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718297, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,343 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718297, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,392 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718297, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.file.AgreementFile?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.file.AgreementFile&logger=log4j&methods=queryAgreementList&pid=96734&server=servletx&side=provider&timestamp=1753799718297], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,405 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,406 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718395, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,407 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718406&pid=96734&server=servletx&side=provider&timestamp=1753799718395 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718395, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,411 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718395, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,449 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718395, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,497 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718395, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718395], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,497 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,498 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,499 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,499 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,500 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,501 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718497, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,501 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,502 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718501&pid=96734&server=servletx&side=provider&timestamp=1753799718497 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718395, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,502 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,503 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,503 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718497, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,506 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,507 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,507 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,508 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,510 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,511 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,512 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,512 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,513 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,514 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,516 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,517 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,517 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,518 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,518 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,519 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,522 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,523 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,525 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,526 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,528 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,530 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,531 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,532 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,537 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,538 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,542 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,546 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,548 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718497, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,550 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,553 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,556 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,559 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,560 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,563 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,564 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,566 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,569 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,570 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,571 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,576 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,581 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,585 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,590 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,593 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,594 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718497, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade&logger=log4j&methods=queryB2BFlightRule,queryB2BFlight,queryDepartureFlight,queryReturnFlight,queryFlight&pid=96734&server=servletx&side=provider&timestamp=1753799718497], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,601 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,606 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,608 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,609 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718596, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,611 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718611&pid=96734&server=servletx&side=provider&timestamp=1753799718596 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718596, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,615 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718596, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,625 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,629 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,634 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,638 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,642 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,645 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,648 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,650 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,655 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718596, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,656 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,659 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,664 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,669 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,672 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,678 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,686 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:18,702 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718596, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718596], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,708 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,709 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718705, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,710 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718709&pid=96734&server=servletx&side=provider&timestamp=1753799718705 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718596, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,713 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718705, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,754 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718705, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,801 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718705, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.order.OrderTimerFacade&logger=log4j&methods=cancelOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718705], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,815 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,815 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718807, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,816 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718816&pid=96734&server=servletx&side=provider&timestamp=1753799718807 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718807, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,819 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718807, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,856 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718807, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,904 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718807, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718807], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,906 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,907 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718905, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,908 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799718907&pid=96734&server=servletx&side=provider&timestamp=1753799718905 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718807, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,910 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718905, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,947 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718905, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:18,995 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718905, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade&logger=log4j&methods=timer,queryOrder&pid=96734&server=servletx&side=provider&timestamp=1753799718905], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799718999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,003 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719003&pid=96734&server=servletx&side=provider&timestamp=1753799718999 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,004 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799718999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,045 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799718999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,089 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799718999, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799718999], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,090 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,090 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719090, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,090 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719090&pid=96734&server=servletx&side=provider&timestamp=1753799719090 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799718999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,091 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719090, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,132 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719090, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,176 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719090, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.ticket.TicketTimerFacade&logger=log4j&methods=queryTicketOrderTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719090], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,185 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,185 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719178, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,186 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719186&pid=96734&server=servletx&side=provider&timestamp=1753799719178 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719178, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,189 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719178, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,227 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719178, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,273 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719178, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719178], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,281 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,281 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719281, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,282 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719281&pid=96734&server=servletx&side=provider&timestamp=1753799719281 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719178, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,288 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719281, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,327 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719281, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,369 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719281, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.timer.fault.FaultRefundTimerFacade&logger=log4j&methods=faultTimer&pid=96734&server=servletx&side=provider&timestamp=1753799719281], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,379 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,379 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719374, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,379 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719379&pid=96734&server=servletx&side=provider&timestamp=1753799719374 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719374, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,381 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719374, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,417 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719374, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,458 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719374, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719374], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,460 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,460 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719460, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,462 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.member.MemberFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719461&pid=96734&server=servletx&side=provider&timestamp=1753799719460 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719374, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,463 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719460, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,498 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719460, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,543 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719460, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.member.MemberFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.member.MemberFacade&logger=log4j&methods=queryMemberByMemberSocialId,initMember,changeMemberInfo,queryMerchantByOpenId&pid=96734&server=servletx&side=provider&timestamp=1753799719460], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,545 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,549 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,549 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,549 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,549 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,549 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,550 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,551 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,551 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,551 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,552 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,552 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,552 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,553 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,553 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,554 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,554 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,555 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,555 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,555 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,555 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,556 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,557 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,558 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,559 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,559 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719550, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,559 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,560 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719560&pid=96734&server=servletx&side=provider&timestamp=1753799719550 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719550, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,561 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,561 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,561 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,562 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,562 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,563 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719550, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,564 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,565 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,567 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,569 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,569 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,570 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,570 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,570 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,571 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,571 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,572 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,572 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,573 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,573 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,578 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,579 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,579 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,582 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,582 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,583 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,586 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,588 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,589 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,590 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,590 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,591 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,592 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,594 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,596 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,601 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719550, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,608 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,641 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,647 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719550, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719550], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,650 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,650 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719648, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,650 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.pay.PayFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719650&pid=96734&server=servletx&side=provider&timestamp=1753799719648 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719550, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,651 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719648, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,688 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719648, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,696 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,723 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,736 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719648, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.pay.PayFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.pay.PayFacade&logger=log4j&methods=orderPay&pid=96734&server=servletx&side=provider&timestamp=1753799719648], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,903 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,903 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719899, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,903 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799719903&pid=96734&server=servletx&side=provider&timestamp=1753799719899 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719899, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,906 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719899, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,944 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719899, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:19,982 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,987 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:19,990 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719899, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719899], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,000 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,000 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,000 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.wx.WxFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799720000&pid=96734&server=servletx&side=provider&timestamp=1753799719999 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799719899, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,001 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,003 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:20,038 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719999, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,040 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:20,042 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:20,082 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719999, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.wx.WxFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.wx.WxFacade&logger=log4j&methods=createMenu,flushWxJsSdkTicketTimer,flushAccessTokenTimer,getWxAccessTokenAndJsSdkFromRedis&pid=96734&server=servletx&side=provider&timestamp=1753799719999], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,196 [WARN ] [DubboSaveRegistryCache-thread-1] AbstractRegistry -  [DUBBO] Failed to save registry store file, cause: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties, dubbo version: 2.5.4, current host: *************
java.io.IOException: Can not lock the registry cache file /Users/<USER>/.dubbo/dubbo-registry-zk.bass.3g.cache, ignore and retry later, maybe multi java process use the file, please config: dubbo.registry.file=xxx.properties
	at com.alibaba.dubbo.registry.support.AbstractRegistry.doSaveProperties(AbstractRegistry.java:184)
	at com.alibaba.dubbo.registry.support.AbstractRegistry$SaveProperties.run(AbstractRegistry.java:518)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:35:20,203 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,204 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720162, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,204 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799720204&pid=96734&server=servletx&side=provider&timestamp=1753799720162 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799720162, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,272 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720162, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,319 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720162, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,361 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720162, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720162], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,363 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,363 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720362, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,363 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799720363&pid=96734&server=servletx&side=provider&timestamp=1753799720362 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799720162, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,375 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720362, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:20,415 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720362, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,430 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720362, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.user.B2bUserFacade&logger=log4j&methods=addUser,queryByUserId,queryByLoginName&pid=96734&server=servletx&side=provider&timestamp=1753799720362], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,784 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,791 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade to url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721636, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,792 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade url hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799721792&pid=96734&server=servletx&side=provider&timestamp=1753799721636 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799721636, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,793 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: hessianx://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721636, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,831 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721636, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,877 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721636, urls: [empty://*************:8081/spacetravel-service-hessian/soa/hessian/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721636], dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,934 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade to local registry, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,934 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Export dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade to url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721922, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,942 [INFO ] [DelayRecoverThread] ServiceConfig -  [DUBBO] Register dubbo service com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade url httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&monitor=soamonitor%3A%2F%2Fsoamonitor%3A9090%3Fdubbo%3D2.5.4%26interface%3Dcom.alibaba.dubbo.monitor.MonitorService%26pid%3D96734%26timestamp%3D1753799721941&pid=96734&server=servletx&side=provider&timestamp=1753799721922 to registry registry://zk.bass.3g:2181/com.alibaba.dubbo.registry.RegistryService?application=spacetravel-service-hessian&check=false&dubbo=2.5.4&file=/Users/<USER>/dubbo-cache/dubbo-registry-spacetravel-service-hessian.cache&logger=log4j&pid=96734&registry=zookeeperx&session=30000&timestamp=1753799721636, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,958 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Register: httpx://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721922, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:21,998 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Subscribe: provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721922, dubbo version: 2.5.4, current host: *************
2025-07-29 22:35:22,045 [INFO ] [DelayRecoverThread] AbstractRegistry -  [DUBBO] Notify urls for subscribe url provider://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721922, urls: [empty://*************:8081/spacetravel-service-hessian/soa/http/com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade?anyhost=true&application=spacetravel-service-hessian&category=configurators&check=false&default.cluster=failfast&default.loadbalance=randomx&default.retries=0&default.service.filter=-exception,-context,-monitor,contextx,exceptionx&dubbo=2.5.4&generic=false&interface=com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade&logger=log4j&methods=refundOrder&pid=96734&server=servletx&side=provider&timestamp=1753799721922], dubbo version: 2.5.4, current host: *************

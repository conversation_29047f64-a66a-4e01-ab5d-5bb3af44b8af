package com.yeepay.spacetravel.service.facade.dto.order.request;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import org.hibernate.validator.constraints.NotBlank;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/10 15:57
 * @Description:
 */
@Getter
@Setter
public class TicketOrderRequestDTO implements Serializable {
    private static final long serialVersionUID = 6675180246136583682L;

    private BigDecimal ticketTotalAmount;
    @NotBlank(message = "memberId不能为空")
    private String memberId;
    @NotBlank(message = "ip不能为空")
    private String userIp;

    /**
     * 航班信息（单程使用）
     */
    private TicketOrderFlightRequestDTO flight;

    /**
     * 去程航班信息（往返程使用）
     */
    private TicketOrderFlightRequestDTO departure;

    /**
     * 回程航班信息（往返程使用）
     */
    private TicketOrderFlightRequestDTO return_;

    /**
     * 是否往返程
     */
    private Boolean isRoundTrip;

    /**
     * 行程类型 OW-单程 RT-往返
     */
    private String journeyType;

    /**
     * 往返程总价
     */
    private BigDecimal totalPrice;

    /**
     * 去程价格
     */
    private BigDecimal departurePrice;

    /**
     * 回程价格
     */
    private BigDecimal returnPrice;

    /**
     * 乘机人信息
     */
    private List<TicketOrderPassengerRequestDTO> passagers;
    /**
     * 联系人信息
     */
    private TicketOrderContactRequestDTO contact;

    /**
     * 是否同意协议
     */
    private Boolean isAgree;
}

package com.yeepay.spacetravel.service.core.service.order;

import java.util.Date;
import java.util.List;

import com.yeepay.spacetravel.common.util.page.Pagenation;
import com.yeepay.spacetravel.service.core.bean.condition.B2BQueryOrderListCondition;
import com.yeepay.spacetravel.service.core.bean.condition.OrderAndRefundPageQueryCondition;
import com.yeepay.spacetravel.service.core.bean.condition.OrderPageQueryCondition;
import com.yeepay.spacetravel.service.core.bean.orderQuery.B2BOrderQueryListBean;
import com.yeepay.spacetravel.service.core.bean.orderQuery.OrderAndRefundQueryListBean;
import com.yeepay.spacetravel.service.core.bean.orderQuery.OrderQueryListBean;
import com.yeepay.spacetravel.service.core.entity.bank.RequestBankEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderSubEntity;
import com.yeepay.spacetravel.service.core.entity.passenger.PassengerEntity;
import com.yeepay.spacetravel.service.core.entity.ticket.RequestTicketEntity;

public interface OrderService {
	public void delete(String id);
	public OrderEntity get(String id);
	public List<OrderEntity> getAll();
	public void add(OrderEntity orderEntity);
	public void update(OrderEntity orderEntity);
	public OrderEntity queryWaitPayOrder(String memberId,List<String> orderStatus,String flightNo,String depTime,List<String> certNums);
	public void saveOrderAndRelationTables(OrderEntity order, List<OrderSubEntity> orderSubs, List<PassengerEntity> passagers, FlightInfoEntity flight);

	/**
	 * 保存订单和关联表信息（支持多个航班信息，用于往返程）
	 */
	public void saveOrderAndRelationTables(OrderEntity order, List<OrderSubEntity> orderSubs, List<PassengerEntity> passagers, List<FlightInfoEntity> flights);
	public void updateOrderAndRequestTicket(OrderEntity order, RequestTicketEntity requestTicket);
	public void updateOrderAndRequestBank(OrderEntity order, RequestBankEntity requestBank);
	public void updateOrderAndRequestTicketAndSubs(OrderEntity order, RequestTicketEntity requestTicket,List<OrderSubEntity> subs);
	public Pagenation<OrderQueryListBean> queryMyOrderListByPage(OrderPageQueryCondition condition);
	public List<OrderEntity> queryOrdersByOutTime(Date start,Date end);
	public void updateOrderAndPayEntities(OrderEntity order,List<RequestBankEntity> payEntities);

	public Pagenation<OrderAndRefundQueryListBean> queryMyOrderAndRefundByPage(OrderAndRefundPageQueryCondition condition);
	public Pagenation<B2BOrderQueryListBean> queryB2BOrderListByPage(B2BQueryOrderListCondition condition);

	public List<OrderEntity> queryUnfinishedStateOrders(Date start,Date end);

}

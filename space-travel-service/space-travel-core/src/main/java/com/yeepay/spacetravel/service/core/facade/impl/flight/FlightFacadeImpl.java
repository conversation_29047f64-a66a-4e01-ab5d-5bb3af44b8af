package com.yeepay.spacetravel.service.core.facade.impl.flight;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.facade.air.ticket.facade.TicketFacade;
import com.yeepay.g3.facade.air.ticket.dto.request.QueryDepartureFlightRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.QueryReturnFlightRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.QueryFlightResponseDTO;
import com.yeepay.g3.utils.rmi.RemotingProtocol;
import com.yeepay.spacetravel.common.util.MultiThread.MultiFutureThread;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.date.DateUtils;
import com.yeepay.spacetravel.common.util.exception.OperationException;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.common.util.redis.RedisIdUtils;
import com.yeepay.spacetravel.common.util.env.IpServerStatisticUtils;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightBaseInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightCabinClsInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.enums.tikcet.TicketServiceEnums;
import com.yeepay.spacetravel.service.core.annotation.BuildFlightInfoAnnotation;
import com.yeepay.spacetravel.service.core.biz.flight.FlightBiz;
import com.yeepay.spacetravel.service.core.biz.impl.flight.FlightTask;
import com.yeepay.spacetravel.service.core.entity.cityAirport.CityAirportEntity;
import com.yeepay.spacetravel.service.core.entity.cityAirport.ProvinceCityEntity;
import com.yeepay.spacetravel.service.core.service.cityAirport.CityAirportService;
import com.yeepay.spacetravel.service.core.service.cityAirport.ProvinceCityService;
import com.yeepay.spacetravel.service.facade.util.FlightCabinClsConvert;
import com.yeepay.spacetravel.service.core.util.Constant;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryB2BFlightInfoDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryB2BRuleRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryFlightInfoDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryDepartureFlightDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryReturnFlightDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.QueryB2BRuleResponseDTO;
import com.yeepay.spacetravel.service.facade.enums.flight.AirCodeEnums;
import com.yeepay.spacetravel.service.facade.enums.flight.Flight2CodeEnum;
import com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.facade.result.b2b.B2bBaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/09 16:50
 * @Description:
 */
@Slf4j
@Service
public class FlightFacadeImpl implements FlightFacade {
    private Logger logger = LoggerFactory.getLogger(FlightFacadeImpl.class);
    @Autowired
    private FlightBiz flightBiz;
    @Autowired
    private CityAirportService cityAirportService;
    @Autowired
    private ProvinceCityService provinceCityService;

    private TicketFacade getTicketFacade(){
        return RemoteServiceFactory.getService(TicketFacade.class);
        //return RemoteServiceFactory.getService("http://ycenc.yeepay.com:30569/air-b2b-hessian/hessian/TicketFacade", RemotingProtocol.HESSIAN,TicketFacade.class);
    }

    @Override
    @BuildFlightInfoAnnotation
    public BaseResult<List<FlightInfoResponseDTO>> queryFlight(QueryFlightInfoDTO queryFlightInfoDTO) {
        logger.info("航班查询facade的入参{}", JSONUtils.toJsonString(queryFlightInfoDTO));
        List<FlightInfoResponseDTO> result = new ArrayList<>();
        try {

            checkRequestBean(queryFlightInfoDTO);

            //通过城市查询机场
            List<CityAirportEntity> deptAirports = getCityAirportList(queryFlightInfoDTO.getDeptCityCode(), queryFlightInfoDTO.getDeptAirport());
            List<CityAirportEntity> arrAirports = getCityAirportList(queryFlightInfoDTO.getArrCityCode(), queryFlightInfoDTO.getArrAirport());

            if (CollectionUtils.isEmpty(deptAirports) || CollectionUtils.isEmpty(arrAirports)) {
                return BaseResult.fail(ReturnCode.PARAM_ERROR, "所选城市不存在机场,不能飞机抵达");
            }

            //要查询的次数
            List<String> airCodes = new ArrayList<>();
            airCodes.add(AirCodeEnums.B2CYPMUNDC.name());
            airCodes = ConfigUtils.getDefaultValue(Constant.SPACE_TRAVEL_NDC_AIR_CODES, airCodes);
            int count = deptAirports.size() * arrAirports.size() * airCodes.size();
            long startTime = System.currentTimeMillis();
            //初始化线程执行类
            MultiFutureThread<List<FlightInfoResponseDTO>> thread = new MultiFutureThread<List<FlightInfoResponseDTO>>(count, count);
            for (CityAirportEntity deptAirport : deptAirports) {
                for (CityAirportEntity arrAirport : arrAirports) {
                    for (String airCode : airCodes) {
                        QueryFlightInfoDTO dto = new QueryFlightInfoDTO();
                        dto.setDeptDate(queryFlightInfoDTO.getDeptDate());
                        dto.setAdultNum(queryFlightInfoDTO.getAdultNum());
                        dto.setChildNum(queryFlightInfoDTO.getChildNum());
                        dto.setDeptAirport(deptAirport.getAirport3Code());
                        dto.setArrAirport(arrAirport.getAirport3Code());
                        dto.setAirCode(airCode);
                        logger.info("放入到task中的参数为{}", JSONUtils.toJsonString(dto));
                        FlightTask task = new FlightTask();
                        task.setQueryFlightInfoDTO(dto);
                        task.setFlightBiz(flightBiz);
                        thread.setCallable(task);
                    }
                }
            }
            //线程启动执行
            List<List<FlightInfoResponseDTO>> list = thread.exec();
            long endTime = System.currentTimeMillis();
            logger.info("多线程处理航班查询耗时{}", startTime - endTime);

            for (List<FlightInfoResponseDTO> flightInfoResponseDTOS : list) {
                if (CollectionUtils.isNotEmpty(flightInfoResponseDTOS)) {
                    for (FlightInfoResponseDTO flightInfoResponseDTO : flightInfoResponseDTOS) {
                        result.add(flightInfoResponseDTO);
                    }
                }
            }


            if (CollectionUtils.isNotEmpty(result)) {
                sortByTime(result);
            }
            return BaseResult.success(result);
        } catch (IllegalArgumentException e) {
            logger.error("查询航班出现参数校验不通过异常", e);
            return BaseResult.fail(ReturnCode.PARAM_ERROR, ReturnCode.getRetMsg(ReturnCode.FAIL));
        } catch (OperationException e) {
            logger.error("查询航班出现内部系统异常", e);
            return BaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询航班出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, "系统开小差稍后重试");
        }
    }

    private List<CityAirportEntity> getCityAirportList(String cityCode, String airportCode) {
        List<CityAirportEntity> arrAirports = cityAirportService.queryAirportByCityCode(cityCode);

        if (StringUtils.isNotBlank(airportCode)) {
            CityAirportEntity cityAirportEntity = new CityAirportEntity();
            cityAirportEntity.setAirport3Code(airportCode);
            arrAirports.add(cityAirportEntity);
        }
        return arrAirports;
    }


    private void checkRequestBean(QueryFlightInfoDTO queryFlightInfoDTO) {

        String deptAirport = queryFlightInfoDTO.getDeptAirport();
        String deptCityCode = queryFlightInfoDTO.getDeptCityCode();
        if (StringUtils.isBlank(deptAirport) && StringUtils.isBlank(deptCityCode)) {
            throw OperationException.newInstantce(ReturnCode.FAIL, "出发机场、出发城市需要选择一个");
        }
        if (StringUtils.isNotBlank(deptAirport) && StringUtils.isNotBlank(deptCityCode)) {
            throw OperationException.newInstantce(ReturnCode.FAIL, "出发机场、出发城市只能选择一个");
        }


        String arrAirport = queryFlightInfoDTO.getArrAirport();
        String arrCityCode = queryFlightInfoDTO.getArrCityCode();
        if (StringUtils.isBlank(arrAirport) && StringUtils.isBlank(arrCityCode)) {
            throw OperationException.newInstantce(ReturnCode.FAIL, "到达机场、到达城市需要选择一个");
        }
        if (StringUtils.isBlank(arrAirport) && StringUtils.isBlank(arrCityCode)) {
            throw OperationException.newInstantce(ReturnCode.FAIL, "到达机场、到达城市只能选择一个");
        }

    }

    @Override
    @BuildFlightInfoAnnotation
    public B2bBaseResult<List<FlightInfoResponseDTO>> queryB2BFlight(QueryB2BFlightInfoDTO queryB2BFlightInfoDTO) {

        logger.info("航班查询queryB2BFlight的入参：{}", JSONUtils.toJsonString(queryB2BFlightInfoDTO));

        List<FlightInfoResponseDTO> result = new ArrayList<>();
        try {
            result = flightBiz.queryB2BFlight(queryB2BFlightInfoDTO);

            if (CollectionUtils.isNotEmpty(result)) {
                sortByTime(result);
            }
            return B2bBaseResult.success(result);
        } catch (IllegalArgumentException e) {
            logger.error("查询B2B航班出现参数校验不通过异常", e);
            return B2bBaseResult.fail(ReturnCode.PARAM_ERROR, ReturnCode.getRetMsg(ReturnCode.FAIL));
        } catch (OperationException e) {
            logger.error("查询B2B航班出现内部系统异常", e);
            return B2bBaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询B2B航班出现未知异常", e);
            return B2bBaseResult.fail(ReturnCode.FAIL, "系统开小差稍后重试");
        }
    }

    @Override
    public BaseResult<List<FlightInfoResponseDTO>> queryDepartureFlight(QueryDepartureFlightDTO queryDepartureFlightDTO) {
        logger.info("往返程去程航班查询facade的入参{}", JSONUtils.toJsonString(queryDepartureFlightDTO));
        List<FlightInfoResponseDTO> result = new ArrayList<>();
        try {
            // 参数校验：必须传入去程和回程的城市/机场信息
            checkDepartureFlightRequestBean(queryDepartureFlightDTO);

            // 通过城市查询机场 - 参考单程查询逻辑
            // 判断传入的是城市代码还是机场代码，智能处理
            List<CityAirportEntity> deptAirports = getAirportListByCode(queryDepartureFlightDTO.getDeptAirport());
            List<CityAirportEntity> arrAirports = getAirportListByCode(queryDepartureFlightDTO.getArrAirport());

            if (CollectionUtils.isEmpty(deptAirports) || CollectionUtils.isEmpty(arrAirports)) {
                return BaseResult.fail(ReturnCode.PARAM_ERROR, "所选城市不存在机场，不能飞机抵达");
            }

            // 使用多线程查询每个机场组合 - 参考单程查询逻辑
            int count = deptAirports.size() * arrAirports.size();
            long startTime = System.currentTimeMillis();
            //初始化线程执行类
            MultiFutureThread<List<FlightInfoResponseDTO>> thread = new MultiFutureThread<List<FlightInfoResponseDTO>>(count, count);
            for (CityAirportEntity deptAirport : deptAirports) {
                for (CityAirportEntity arrAirport : arrAirports) {
                    logger.info("放入到NDC往返程task中的机场组合：{}->{}", deptAirport.getAirport3Code(), arrAirport.getAirport3Code());
                    NDCDepartureFlightTask task = new NDCDepartureFlightTask();
                    task.setDeptAirportCode(deptAirport.getAirport3Code());
                    task.setArrAirportCode(arrAirport.getAirport3Code());
                    task.setQueryDepartureFlightDTO(queryDepartureFlightDTO);
                    task.setFlightFacade(this);
                    thread.setCallable(task);
                }
            }
            //线程启动执行
            List<List<FlightInfoResponseDTO>> list = thread.exec();
            long endTime = System.currentTimeMillis();
            logger.info("多线程处理NDC往返程航班查询耗时{}ms", endTime - startTime);

            boolean ndcSuccess = false;
            for (List<FlightInfoResponseDTO> flightInfoResponseDTOS : list) {
                if (CollectionUtils.isNotEmpty(flightInfoResponseDTOS)) {
                    for (FlightInfoResponseDTO flightInfoResponseDTO : flightInfoResponseDTOS) {
                        result.add(flightInfoResponseDTO);
                    }
                    ndcSuccess = true;
                }
            }

            // 如果NDC接口没有获得结果，启用降级逻辑
            if (!ndcSuccess || CollectionUtils.isEmpty(result)) {
                logger.warn("NDC往返程接口未获得航班数据，启用降级逻辑使用单程接口查询");
                return queryDepartureFlightWithFallback(queryDepartureFlightDTO);
            }

            // 按时间排序
            if (CollectionUtils.isNotEmpty(result)) {
                sortByTime(result);
            }

            logger.info("往返程去程查询成功，总共返回{}条航班", result.size());
            return BaseResult.success(result);

        } catch (IllegalArgumentException e) {
            logger.error("查询往返程去程航班出现参数校验不通过异常", e);
            return BaseResult.fail(ReturnCode.PARAM_ERROR, e.getMessage());
        } catch (OperationException e) {
            logger.error("查询往返程去程航班出现内部系统异常", e);
            return BaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询往返程去程航班出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, "系统开小差稍后重试");
        }
    }

    @Override
    public BaseResult<List<FlightInfoResponseDTO>> queryReturnFlight(QueryReturnFlightDTO queryReturnFlightDTO) {
        logger.info("往返程回程航班查询facade的入参{}", JSONUtils.toJsonString(queryReturnFlightDTO));
        List<FlightInfoResponseDTO> result = new ArrayList<>();
        try {
            // 参数校验
            checkReturnFlightRequestBean(queryReturnFlightDTO);

            // 通过城市查询机场 - 参考单程查询逻辑
            // 判断传入的是城市代码还是机场代码，智能处理
            List<CityAirportEntity> deptAirports = getAirportListByCode(queryReturnFlightDTO.getDeptAirport());
            List<CityAirportEntity> arrAirports = getAirportListByCode(queryReturnFlightDTO.getArrAirport());

            if (CollectionUtils.isEmpty(deptAirports) || CollectionUtils.isEmpty(arrAirports)) {
                return BaseResult.fail(ReturnCode.PARAM_ERROR, "所选城市不存在机场，不能飞机抵达");
            }

            // 检查是否有去程选择信息，如果有则尝试NDC回程接口
            boolean hasValidDepartureOffer = CollectionUtils.isNotEmpty(queryReturnFlightDTO.getDepartureOffers());
            if (hasValidDepartureOffer) {
                for (QueryReturnFlightDTO.DepartureOffer departureOffer : queryReturnFlightDTO.getDepartureOffers()) {
                    if (departureOffer == null ||
                            StringUtils.isBlank(departureOffer.getCabinPriceId()) ||
                            StringUtils.isBlank(departureOffer.getCabinPriceItemId())) {
                        hasValidDepartureOffer = false;
                        break;
                    }
                }
            }else {
                log.warn("没有有效的去程选择信息，直接启用降级逻辑。去程选择信息");
            }

            // 如果有有效的去程选择信息，使用多线程查询每个机场组合
            boolean ndcSuccess = false;
            if (hasValidDepartureOffer) {
                int count = deptAirports.size() * arrAirports.size();
                long startTime = System.currentTimeMillis();
                //初始化线程执行类
                MultiFutureThread<List<FlightInfoResponseDTO>> thread = new MultiFutureThread<List<FlightInfoResponseDTO>>(count, count);
                for (CityAirportEntity deptAirport : deptAirports) {
                    for (CityAirportEntity arrAirport : arrAirports) {
                        logger.info("放入到NDC往返程回程task中的机场组合：{}->{}", deptAirport.getAirport3Code(), arrAirport.getAirport3Code());
                        NDCReturnFlightTask task = new NDCReturnFlightTask();
                        task.setDeptAirportCode(deptAirport.getAirport3Code());
                        task.setArrAirportCode(arrAirport.getAirport3Code());
                        task.setQueryReturnFlightDTO(queryReturnFlightDTO);
                        task.setFlightFacade(this);
                        thread.setCallable(task);
                    }
                }
                //线程启动执行
                List<List<FlightInfoResponseDTO>> list = thread.exec();
                long endTime = System.currentTimeMillis();
                logger.info("多线程处理NDC往返程回程航班查询耗时{}ms", endTime - startTime);

                for (List<FlightInfoResponseDTO> flightInfoResponseDTOS : list) {
                    if (CollectionUtils.isNotEmpty(flightInfoResponseDTOS)) {
                        for (FlightInfoResponseDTO flightInfoResponseDTO : flightInfoResponseDTOS) {
                            result.add(flightInfoResponseDTO);
                        }
                        ndcSuccess = true;
                    }
                }
            } else {
                logger.warn("NDC往返程回程查询缺少有效去程选择信息，直接启用降级逻辑。去程选择信息: {}",
                        JSONUtils.toJsonString(queryReturnFlightDTO.getDepartureOffers()));
            }

            // 如果NDC接口没有获得结果，启用降级逻辑
            if (!ndcSuccess || CollectionUtils.isEmpty(result)) {
                logger.warn("NDC往返程回程接口未获得航班数据，启用降级逻辑使用单程接口查询");
                return queryReturnFlightWithFallback(queryReturnFlightDTO);
            }

            // 按时间排序
            if (CollectionUtils.isNotEmpty(result)) {
                sortByTime(result);
            }

            logger.info("往返程回程查询成功，总共返回{}条航班", result.size());
            return BaseResult.success(result);

        } catch (IllegalArgumentException e) {
            logger.error("查询往返程回程航班出现参数校验不通过异常", e);
            return BaseResult.fail(ReturnCode.PARAM_ERROR, e.getMessage());
        } catch (OperationException e) {
            logger.error("查询往返程回程航班出现内部系统异常", e);
            return BaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询往返程回程航班出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, "系统开小差稍后重试");
        }
    }

    /**
     * 转换B2B服务响应为前端格式
     */
    public List<FlightInfoResponseDTO> convertB2BFlightResponse(QueryFlightResponseDTO b2bResponse) {
        List<FlightInfoResponseDTO> result = new ArrayList<>();

        if (b2bResponse == null || b2bResponse.getJourney() == null ||
                CollectionUtils.isEmpty(b2bResponse.getJourney().getOfferList())) {
            logger.warn("B2B响应数据为空或无可用报价: response={}, journey={}, offerListSize={}",
                    b2bResponse != null,
                    b2bResponse != null && b2bResponse.getJourney() != null,
                    b2bResponse != null && b2bResponse.getJourney() != null && b2bResponse.getJourney().getOfferList() != null ?
                            b2bResponse.getJourney().getOfferList().size() : 0);
            return result;
        }

        logger.info("开始转换B2B响应数据，共{}个报价信息", b2bResponse.getJourney().getOfferList().size());

        // 获取行李额度映射
        Map<String, QueryFlightResponseDTO.BaggageAllowance> baggageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(b2bResponse.getBaggageAllowanceList())) {
            for (QueryFlightResponseDTO.BaggageAllowance baggage : b2bResponse.getBaggageAllowanceList()) {
                if (StringUtils.isNotBlank(baggage.getBaggageAllowanceId())) {
                    baggageMap.put(baggage.getBaggageAllowanceId(), baggage);
                }
            }
        }

        // 遍历每个报价offer
        for (QueryFlightResponseDTO.Offer offer : b2bResponse.getJourney().getOfferList()) {
            if (CollectionUtils.isEmpty(offer.getSegmentList()) || CollectionUtils.isEmpty(offer.getCabinPriceList())) {
                logger.warn("跳过无效offer：航段数={}, 舱位价格数={}",
                        offer.getSegmentList() != null ? offer.getSegmentList().size() : 0,
                        offer.getCabinPriceList() != null ? offer.getCabinPriceList().size() : 0);
                continue;
            }

            // 创建航班信息响应
            FlightInfoResponseDTO flightInfo = new FlightInfoResponseDTO();

            // 1. 转换航班基本信息（使用第一个航段信息）
            QueryFlightResponseDTO.Segment firstSegment = offer.getSegmentList().get(0);
            FlightBaseInfoResponseDTO baseInfo = convertSegmentToBaseInfo(firstSegment);
            flightInfo.setFlightBaseInfoResponseDTO(baseInfo);

            // 2. 转换舱位信息列表
            List<FlightCabinClsInfoResponseDTO> cabinList = new ArrayList<>();
            for (QueryFlightResponseDTO.CabinPrice cabinPrice : offer.getCabinPriceList()) {
                FlightCabinClsInfoResponseDTO cabinInfo = convertCabinPriceToInfo(cabinPrice, baggageMap, firstSegment);
                if (cabinInfo != null) {
                    cabinList.add(cabinInfo);
                }
            }
            flightInfo.setFlightCabinClsInfoResponseDTOs(cabinList);

            if (CollectionUtils.isNotEmpty(cabinList)) {
                result.add(flightInfo);
            } else {
                logger.warn("航班{}转换后舱位信息为空，跳过", baseInfo.getFlightNo());
            }
        }

        logger.info("B2B数据转换完成，共转换{}条航班信息", result.size());
        return result;
    }

    /**
     * 转换航段信息为航班基本信息
     */
    private FlightBaseInfoResponseDTO convertSegmentToBaseInfo(QueryFlightResponseDTO.Segment segment) {
        FlightBaseInfoResponseDTO baseInfo = new FlightBaseInfoResponseDTO();

        // 基本航班信息
        baseInfo.setFlightNo(segment.getFlightNo());
        baseInfo.setAirCode(segment.getFlightCode());
        baseInfo.setDeptAirport(segment.getDeptAirportCode());
        baseInfo.setArrAirport(segment.getArrAirportCode());
        baseInfo.setDeptCityName(segment.getDeptCityName());
        baseInfo.setArrCityName(segment.getArrCityName());
        baseInfo.setPlaneType(segment.getPlaneType());
        baseInfo.setDepTerminal(segment.getDepTerminal());
        baseInfo.setArrTerminal(segment.getArrTerminal());

        // 时间信息处理（增加安全性检查）
        try {
            if (StringUtils.isNotBlank(segment.getDeptDateTime()) && segment.getDeptDateTime().length() >= 16) {
                baseInfo.setDeptTime(segment.getDeptDateTime().substring(11, 16)); // 提取时间部分 HH:mm
                baseInfo.setDeptDate(segment.getDeptDateTime().substring(0, 10)); // 提取日期部分 yyyy-MM-dd
            }
            if (StringUtils.isNotBlank(segment.getArrDateTime()) && segment.getArrDateTime().length() >= 16) {
                baseInfo.setArrTime(segment.getArrDateTime().substring(11, 16)); // 提取时间部分 HH:mm
            }
        } catch (Exception e) {
            logger.warn("时间格式解析异常，跳过时间设置: 出发={}, 到达={}", segment.getDeptDateTime(), segment.getArrDateTime());
        }

        // 经停信息
        baseInfo.setStop(segment.getStop() != null && segment.getStop() ? "Y" : "N");

        // 共享航班信息
        baseInfo.setShare(segment.getShare());
        baseInfo.setOperateFlightNo(segment.getOperateFlightNo());

        // 币种固定为人民币
        baseInfo.setCurrencyCode("CNY");

        // 查询并设置机场名称和城市名称
        try {
            // 查询出发机场信息
            if (StringUtils.isNotBlank(segment.getDeptAirportCode())) {
                CityAirportEntity deptCityAirportEntity = cityAirportService.queryByAirPort3Code(segment.getDeptAirportCode());
                if (deptCityAirportEntity != null) {
                    baseInfo.setDeptAirportName(deptCityAirportEntity.getAirportName());
                    // 如果B2B响应中没有城市名称，通过机场查询城市名称
                    if (StringUtils.isBlank(baseInfo.getDeptCityName()) && StringUtils.isNotBlank(deptCityAirportEntity.getCityCode())) {
                        ProvinceCityEntity deptProvinceCity = provinceCityService.queryByCode(deptCityAirportEntity.getCityCode());
                        if (deptProvinceCity != null) {
                            baseInfo.setDeptCityName(deptProvinceCity.getName());
                        }
                    }
                }
            }

            // 查询到达机场信息
            if (StringUtils.isNotBlank(segment.getArrAirportCode())) {
                CityAirportEntity arrCityAirportEntity = cityAirportService.queryByAirPort3Code(segment.getArrAirportCode());
                if (arrCityAirportEntity != null) {
                    baseInfo.setArrAirportName(arrCityAirportEntity.getAirportName());
                    // 如果B2B响应中没有城市名称，通过机场查询城市名称
                    if (StringUtils.isBlank(baseInfo.getArrCityName()) && StringUtils.isNotBlank(arrCityAirportEntity.getCityCode())) {
                        ProvinceCityEntity arrProvinceCity = provinceCityService.queryByCode(arrCityAirportEntity.getCityCode());
                        if (arrProvinceCity != null) {
                            baseInfo.setArrCityName(arrProvinceCity.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("查询机场名称失败，跳过机场名称设置: 出发机场={}, 到达机场={}, 错误={}",
                    segment.getDeptAirportCode(), segment.getArrAirportCode(), e.getMessage());
        }

        // 手动设置航司图标 - 确保NDC接口返回的数据也包含航司图标
        try {
            if (StringUtils.isNotBlank(baseInfo.getAirCode())) {
                String air2Code = getAir2Code(baseInfo.getAirCode());
                baseInfo.setAirName(Flight2CodeEnum.getAirName(air2Code));
                baseInfo.setAirIconUrl("https://img.yeepay.com/spaceTravel/img/icon/" + air2Code + ".png");
                
                // 设置共享航班的航司图标
                if (StringUtils.isNotBlank(baseInfo.getOperateFlightNo()) && baseInfo.getOperateFlightNo().length() >= 2) {
                    String operateAir2Code = baseInfo.getOperateFlightNo().substring(0, 2);
                    baseInfo.setOperateAirName(Flight2CodeEnum.getAirName(operateAir2Code));
                    baseInfo.setOperateIconUrl("https://img.yeepay.com/spaceTravel/img/icon/" + operateAir2Code + ".png");
                }
            }
        } catch (Exception e) {
            logger.warn("设置航司图标失败: 航司代码={}, 错误={}", baseInfo.getAirCode(), e.getMessage());
        }

        return baseInfo;
    }

    /**
     * 转换舱位价格信息为舱位信息
     */
    private FlightCabinClsInfoResponseDTO convertCabinPriceToInfo(QueryFlightResponseDTO.CabinPrice cabinPrice,
                                                                  Map<String, QueryFlightResponseDTO.BaggageAllowance> baggageMap, QueryFlightResponseDTO.Segment segment) {

        if (CollectionUtils.isEmpty(cabinPrice.getCabinPriceItemList())) {
            logger.warn("跳过无效舱位价格：价格项为空，舱位价格ID={}", cabinPrice.getCabinPriceId());
            return null;
        }

        FlightCabinClsInfoResponseDTO cabinInfo = new FlightCabinClsInfoResponseDTO();

        // 使用成人价格作为主要价格信息
        QueryFlightResponseDTO.CabinPriceItem adultPriceItem = null;
        QueryFlightResponseDTO.CabinPriceItem childPriceItem = null;

        for (QueryFlightResponseDTO.CabinPriceItem item : cabinPrice.getCabinPriceItemList()) {
            if (item != null && StringUtils.isNotBlank(item.getPassengerType())) {
                if ("ADULT".equals(item.getPassengerType())) {
                    adultPriceItem = item;
                } else if ("CHILD".equals(item.getPassengerType())) {
                    childPriceItem = item;
                }
            }
        }

        if (adultPriceItem == null) {
            logger.warn("跳过无效舱位价格：无成人价格信息，舱位价格ID={}", cabinPrice.getCabinPriceId());
            return null;
        }

        logger.debug("开始转换舱位信息：舱位价格ID={}，成人价格项ID={}",
                cabinPrice.getCabinPriceId(), adultPriceItem.getCabinPriceItemId());

        // 设置价格信息（B2B响应中的价格字段已经是BigDecimal类型，直接赋值）

        // 成人价格信息
        if (adultPriceItem.getTicketPrice() != null) {
            cabinInfo.setTicketParAdult(adultPriceItem.getTicketPrice()); // 成人票面价（单个）
            cabinInfo.setTicketPar(adultPriceItem.getTicketPrice()); // 总票面价（兼容性）
        }
        if (adultPriceItem.getPrice() != null) {
            cabinInfo.setPriceAdult(adultPriceItem.getPrice()); // 成人价格（销售价）
        }
        if (adultPriceItem.getTotalPrice() != null) {
            cabinInfo.setPayPriceAdult(adultPriceItem.getTotalPrice()); // 成人总支付金额
            cabinInfo.setPayPrice(adultPriceItem.getTotalPrice()); // 支付价格（兼容性）
        }

        // 税费信息
        if (adultPriceItem.getTotalTax() != null) {
            cabinInfo.setTax(adultPriceItem.getTotalTax()); // 总税费
            cabinInfo.setTaxFeeAdult(adultPriceItem.getTotalTax().toString()); // 成人机建税（字符串格式）
        }
        if (adultPriceItem.getFuelTax() != null) {
            cabinInfo.setOilFeeAdult(adultPriceItem.getFuelTax().toString()); // 燃油费（字符串格式）
        }
        if (adultPriceItem.getConstructionFee() != null) {
            cabinInfo.setAmtAdultAirPortFee(adultPriceItem.getConstructionFee().toString()); // 机建费（字符串格式）
        }

        // 如果有儿童价格
        if (childPriceItem != null) {
            if (childPriceItem.getTicketPrice() != null) {
                cabinInfo.setTicketParChild(childPriceItem.getTicketPrice()); // 儿童票面价（单个）
                cabinInfo.setPriceChild(childPriceItem.getPrice()); // 儿童价格
            }
            if (childPriceItem.getTotalTax() != null) {
                cabinInfo.setTaxFeeChild(childPriceItem.getTotalTax()); // 儿童税费（单个）
            }
            if (childPriceItem.getTotalPrice() != null) {
                cabinInfo.setPayPriceChild(childPriceItem.getTotalPrice()); // 儿童总支付金额
            }
            if (childPriceItem.getFuelTax() != null) {
                cabinInfo.setOilFeeChild(childPriceItem.getFuelTax()); // 儿童燃油费
            }
            if (childPriceItem.getConstructionFee() != null) {
                cabinInfo.setAmtChildAirPortFee(childPriceItem.getConstructionFee()); // 儿童机建费
            }
        }

        // 设置舱位信息（使用成人价格项中的第一个舱位信息）
        if (CollectionUtils.isNotEmpty(adultPriceItem.getCabinInfoList())) {
            QueryFlightResponseDTO.CabinInfo firstCabin = adultPriceItem.getCabinInfoList().get(0);
            if (firstCabin != null) {
                // 设置舱位代码和类型
                cabinInfo.setCabinCls(firstCabin.getCabinCode());
                cabinInfo.setCabinType(firstCabin.getCabinType());

                // 使用航司代码正确转换舱位名称
                String airCode = segment != null ? segment.getFlightCode() : "CZ"; // 默认使用CZ
                cabinInfo.setCabinClsName(FlightCabinClsConvert.getCabinName(airCode, firstCabin.getCabinCode()));

                // 设置座位数（如果可用）
                if (StringUtils.isNotBlank(firstCabin.getSeatNum())) {
                    // 注意：FlightCabinClsInfoResponseDTO 可能没有seatNum字段，这里记录日志
                    logger.debug("舱位{}可用座位数: {}", firstCabin.getCabinCode(), firstCabin.getSeatNum());
                }

                // 设置行李额度
                if (CollectionUtils.isNotEmpty(firstCabin.getBaggageAllowanceIdList())) {
                    String firstBaggageId = firstCabin.getBaggageAllowanceIdList().get(0);
                    if (StringUtils.isNotBlank(firstBaggageId)) {
                        QueryFlightResponseDTO.BaggageAllowance baggage = baggageMap.get(firstBaggageId);
                        if (baggage != null && StringUtils.isNotBlank(baggage.getValue()) && StringUtils.isNotBlank(baggage.getUnit())) {
                            cabinInfo.setLuggage(baggage.getValue() + baggage.getUnit());
                        }
                    }
                }
            }
        } else {
            logger.warn("舱位价格项{}中没有舱位信息列表", adultPriceItem.getCabinPriceItemId());
        }

        // 设置其他价格相关信息
        if (adultPriceItem.getPromotionAmount() != null) {
            cabinInfo.setCouponAmount(adultPriceItem.getPromotionAmount()); // 优惠金额/营销金额
        }
        if (adultPriceItem.getTicketPrice() != null) {
            cabinInfo.setAdultOrigPrice(adultPriceItem.getTicketPrice()); // 成人原价（通常等于票面价）
        }
        if (StringUtils.isNotBlank(adultPriceItem.getProductType())) {
            cabinInfo.setProductType(adultPriceItem.getProductType()); // 产品类型
            // 根据产品类型设置产品说明
            cabinInfo.setProductDesc(getProductDescription(adultPriceItem.getProductType()));
        }

        // 设置政策ID（防空处理）
        if (StringUtils.isNotBlank(adultPriceItem.getCabinPriceItemId())) {
            cabinInfo.setAdultCabinPriceItemId(adultPriceItem.getCabinPriceItemId());
        }
        if (StringUtils.isNotBlank(cabinPrice.getCabinPriceId())) {
            cabinInfo.setCabinPriceId(cabinPrice.getCabinPriceId());
        }
        if (childPriceItem != null && StringUtils.isNotBlank(childPriceItem.getCabinPriceItemId())) {
            cabinInfo.setChildCabinPriceItemId(childPriceItem.getCabinPriceItemId()); // 儿童政策ID
        }

        // 设置餐食信息（从航段信息获取，默认为有餐食）
        String mealInfo = "Y"; // 默认值
        if (segment != null && StringUtils.isNotBlank(segment.getMeal())) {
            mealInfo = segment.getMeal();
        }
        cabinInfo.setMeal(mealInfo);

        // 设置产品代码
        cabinInfo.setProductCode("NDC");

        // 设置价格类型（NDC产品标记为正常价格）
        cabinInfo.setPriceType("NORMAL");

        // 输出调试信息
        logger.debug("舱位信息转换完成：舱位={}, 舱位名称={}, 餐食={}, 行李={}, 成人票价={}, 成人总价={}",
                cabinInfo.getCabinCls(), cabinInfo.getCabinClsName(), cabinInfo.getMeal(),
                cabinInfo.getLuggage(), cabinInfo.getTicketParAdult(), cabinInfo.getPayPriceAdult());

        return cabinInfo;
    }

    /**
     * 根据产品类型获取产品说明
     */
    private String getProductDescription(String productType) {
        if (StringUtils.isBlank(productType)) {
            return "标准产品";
        }

        // 根据实际业务需求定义产品类型说明
        switch (productType.toUpperCase()) {
            case "PREMIUM":
                return "优选产品";
            case "DISCOUNT":
                return "特价产品";
            case "REFUNDABLE":
                return "可退产品";
            case "NON_REFUNDABLE":
                return "不可退产品";
            case "FLEXIBLE":
                return "灵活产品";
            default:
                return "标准产品";
        }
    }

    /**
     * 航司代码处理，去除前缀后缀
     */
    private String getAir2Code(String air2Code) {
        if (StringUtils.isBlank(air2Code)) {
            return air2Code;
        }
        air2Code = air2Code.replace("B2CYP", "");
        air2Code = air2Code.replace("_NDC2C", "");
        air2Code = air2Code.replace("NDC", "");
        air2Code = air2Code.replace("AIR_", "");
        air2Code = air2Code.replace("_TRAVELNDC", "");
        return air2Code;
    }

    @Override
    public B2bBaseResult<QueryB2BRuleResponseDTO> queryB2BFlightRule(QueryB2BRuleRequestDTO queryB2BRuleRequestDTO) {

        logger.info("查询退改规则的入参：{}", JSONUtils.toJsonString(queryB2BRuleRequestDTO));

        try {
            QueryB2BRuleResponseDTO result = flightBiz.queryB2BFlightRule(queryB2BRuleRequestDTO);

            return B2bBaseResult.success(result);
        } catch (IllegalArgumentException e) {
            logger.error("查询退改规则出现参数校验不通过异常", e);
            return B2bBaseResult.fail(ReturnCode.PARAM_ERROR, ReturnCode.getRetMsg(ReturnCode.FAIL));
        } catch (OperationException e) {
            logger.error("查询退改规则出现内部系统异常", e);
            return B2bBaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询退改规则出现未知异常", e);
            return B2bBaseResult.fail(ReturnCode.FAIL, "系统开小差稍后重试");
        }
    }


    public void sortByTime(List<FlightInfoResponseDTO> result) {
        result.sort(new Comparator<FlightInfoResponseDTO>() {
            @Override
            public int compare(FlightInfoResponseDTO o1, FlightInfoResponseDTO o2) {
                int flag = -1;
                try {
                    Date o2Deptime = DateUtils.sdfDateTime.parse(o2.getFlightBaseInfoResponseDTO().getDeptTime() + ":00");
                    Date o1Deptime = DateUtils.sdfDateTime.parse(o1.getFlightBaseInfoResponseDTO().getDeptTime() + ":00");
                    flag = o1Deptime.compareTo(o2Deptime);
                } catch (Exception e) {
                    logger.info("返回航班结果排序过程中出现位置异常，跳过这个进行下一个排队");
                }
                return flag;
            }
        });
    }

    /**
     * 获取B2B商户信息
     */
    private Map<String, String> getRequestB2bInfo() {
        Map<String, String> info = new HashMap<>();
        logger.info("当前处理逻辑所在环境是否为内测{}", IpServerStatisticUtils.getEnvironmentTest());
        if (IpServerStatisticUtils.getEnvironmentTest()) {
            info = ConfigUtils.requestB2bMerchantInfoForNc(TicketServiceEnums.AIRB2B.name());
        } else {
            info = ConfigUtils.requestB2bMerchantInfo(TicketServiceEnums.AIRB2B.name());
        }
        return info;
    }

    /**
     * 获取B2B商户标识
     */
    public String getMerchantCustomerSign() {
        return "YP10088876211";
        //return getRequestB2bInfo().get("customerSign");
    }

    /**
     * 往返程查询降级逻辑：使用单程接口分别查询去程和回程
     */
    @BuildFlightInfoAnnotation  // 添加注解确保AOP切面处理
    private BaseResult<List<FlightInfoResponseDTO>> queryDepartureFlightWithFallback(QueryDepartureFlightDTO queryDepartureFlightDTO) {
        logger.info("启用往返程查询降级逻辑，使用单程接口查询");
        logger.info("降级查询参数: 出发机场={}, 到达机场={}, 去程日期={}, 回程日期={}, 成人数={}, 儿童数={}",
                queryDepartureFlightDTO.getDeptAirport(), queryDepartureFlightDTO.getArrAirport(),
                queryDepartureFlightDTO.getDeptDate(), queryDepartureFlightDTO.getReturnDate(),
                queryDepartureFlightDTO.getAdultNum(), queryDepartureFlightDTO.getChildNum());

        try {
            List<FlightInfoResponseDTO> allFlights = new ArrayList<>();
            boolean hasOutbound = false;
            boolean hasReturn = false;

            // 构建去程查询参数
            QueryFlightInfoDTO outboundQuery = new QueryFlightInfoDTO();

            // 智能设置机场和城市代码
            String deptAirportCode = queryDepartureFlightDTO.getDeptAirport();
            String arrAirportCode = queryDepartureFlightDTO.getArrAirport();

            // 判断传入的是城市代码还是机场代码，并相应设置
            if (isCityCode(deptAirportCode)) {
                logger.info("检测到出发地使用城市代码: {}", deptAirportCode);
                outboundQuery.setDeptCityCode(deptAirportCode);
                outboundQuery.setDeptAirport(""); // 让系统自动查找该城市的机场
            } else {
                logger.info("检测到出发地使用机场代码: {}", deptAirportCode);
                outboundQuery.setDeptAirport(deptAirportCode);
                outboundQuery.setDeptCityCode(""); // 机场代码时城市代码留空
            }

            if (isCityCode(arrAirportCode)) {
                logger.info("检测到到达地使用城市代码: {}", arrAirportCode);
                outboundQuery.setArrCityCode(arrAirportCode);
                outboundQuery.setArrAirport(""); // 让系统自动查找该城市的机场
            } else {
                logger.info("检测到到达地使用机场代码: {}", arrAirportCode);
                outboundQuery.setArrAirport(arrAirportCode);
                outboundQuery.setArrCityCode(""); // 机场代码时城市代码留空
            }

            outboundQuery.setDeptDate(queryDepartureFlightDTO.getDeptDate());
            outboundQuery.setAdultNum(queryDepartureFlightDTO.getAdultNum() != null ? queryDepartureFlightDTO.getAdultNum() : "1");
            outboundQuery.setChildNum(queryDepartureFlightDTO.getChildNum() != null ? queryDepartureFlightDTO.getChildNum() : "0");

            logger.info("去程查询参数: 出发城市={}, 出发机场={}, 到达城市={}, 到达机场={}, 日期={}",
                    outboundQuery.getDeptCityCode(), outboundQuery.getDeptAirport(),
                    outboundQuery.getArrCityCode(), outboundQuery.getArrAirport(), outboundQuery.getDeptDate());

            // 查询去程航班 - 直接调用FlightBiz（降级方法的AOP会处理航司图标）
            List<FlightInfoResponseDTO> outboundFlights = flightBiz.queryFlight(outboundQuery);
            if (CollectionUtils.isNotEmpty(outboundFlights)) {
                // 为去程航班添加标识
                for (FlightInfoResponseDTO flight : outboundFlights) {
                    // 可以在这里添加去程标识
                    allFlights.add(flight);
                }
                hasOutbound = true;
                logger.info("降级查询去程航班成功，获得{}条航班", outboundFlights.size());
            } else {
                logger.warn("降级查询去程航班失败: 返回结果为空");
            }

            // 构建回程查询参数
            if (StringUtils.isNotBlank(queryDepartureFlightDTO.getReturnDate())) {
                QueryFlightInfoDTO returnQuery = new QueryFlightInfoDTO();

                // 回程：出发地和到达地对调
                if (isCityCode(arrAirportCode)) {
                    returnQuery.setDeptCityCode(arrAirportCode); // 回程出发地=去程到达地
                    returnQuery.setDeptAirport("");
                } else {
                    returnQuery.setDeptAirport(arrAirportCode);
                    returnQuery.setDeptCityCode("");
                }

                if (isCityCode(deptAirportCode)) {
                    returnQuery.setArrCityCode(deptAirportCode); // 回程到达地=去程出发地
                    returnQuery.setArrAirport("");
                } else {
                    returnQuery.setArrAirport(deptAirportCode);
                    returnQuery.setArrCityCode("");
                }

                returnQuery.setDeptDate(queryDepartureFlightDTO.getReturnDate());
                returnQuery.setAdultNum(queryDepartureFlightDTO.getAdultNum() != null ? queryDepartureFlightDTO.getAdultNum() : "1");
                returnQuery.setChildNum(queryDepartureFlightDTO.getChildNum() != null ? queryDepartureFlightDTO.getChildNum() : "0");

                logger.info("回程查询参数: 出发城市={}, 出发机场={}, 到达城市={}, 到达机场={}, 日期={}",
                        returnQuery.getDeptCityCode(), returnQuery.getDeptAirport(),
                        returnQuery.getArrCityCode(), returnQuery.getArrAirport(), returnQuery.getDeptDate());

                // 查询回程航班 - 直接调用FlightBiz（降级方法的AOP会处理航司图标）
                List<FlightInfoResponseDTO> returnFlights = flightBiz.queryFlight(returnQuery);
                if (CollectionUtils.isNotEmpty(returnFlights)) {
                    // 为回程航班添加标识
                    for (FlightInfoResponseDTO flight : returnFlights) {
                        // 可以在这里添加回程标识
                        allFlights.add(flight);
                    }
                    hasReturn = true;
                    logger.info("降级查询回程航班成功，获得{}条航班", returnFlights.size());
                } else {
                    logger.warn("降级查询回程航班失败: 返回结果为空");
                }
            } else {
                logger.warn("回程日期为空，跳过回程航班查询");
            }

            // 结果处理
            if (CollectionUtils.isNotEmpty(allFlights)) {
                // 按时间排序
                sortByTime(allFlights);
                logger.info("降级查询成功！总共获得{}条航班（去程: {}, 回程: {}）",
                        allFlights.size(), hasOutbound ? "成功" : "失败", hasReturn ? "成功" : "失败");
                return BaseResult.success(allFlights);
            } else {
                String errorMsg = "降级查询未获得任何航班";
                if (!hasOutbound && !hasReturn) {
                    errorMsg += "（去程和回程查询均失败）";
                } else if (!hasOutbound) {
                    errorMsg += "（去程查询失败）";
                } else if (!hasReturn) {
                    errorMsg += "（回程查询失败）";
                }
                logger.warn(errorMsg);
                return BaseResult.fail(ReturnCode.FAIL, "暂无可用航班");
            }

        } catch (Exception e) {
            logger.error("降级查询异常", e);
            return BaseResult.fail(ReturnCode.FAIL, "查询航班异常: " + e.getMessage());
        }
    }

    /**
     * 判断是否为城市代码
     * 根据常见的城市代码模式进行判断
     */
    private boolean isCityCode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }

        // 常见的城市代码（三字母，但不是标准机场代码）
        String[] cityCodes = {
                "BJS", // 北京
                "SHA", // 上海（注意：SHA也是上海虹桥机场代码，这里需要根据实际业务逻辑判断）
                "CAN", // 广州
                "SZX", // 深圳
                "CTU", // 成都
                "WUH", // 武汉
                "NKG", // 南京
                "HGH", // 杭州
                "XIY", // 西安
                "KMG", // 昆明
                "URC", // 乌鲁木齐
                "LHW", // 兰州
                "SJW", // 石家庄
                "TAO", // 青岛
                "TSN", // 天津
                "DLC", // 大连
                "SHE", // 沈阳
                "CGO", // 郑州
                "WNZ", // 温州
                "FOC", // 福州
                "XMN", // 厦门
                "NNG", // 南宁
                "HRB", // 哈尔滨
                "CGQ", // 长春
                "JNZ"  // 济南
        };

        // 简单判断：如果是已知的城市代码，返回true
        for (String cityCode : cityCodes) {
            if (cityCode.equals(code)) {
                return true;
            }
        }

        // 如果代码长度为3且全为大写字母，且不在已知机场代码列表中，可能是城市代码
        // 这里可以根据实际业务需求进一步完善判断逻辑
        return false;
    }

    /**
     * 往返程回程查询降级逻辑：使用单程接口查询回程航班
     */
    @BuildFlightInfoAnnotation  // 添加注解确保AOP切面处理
    private BaseResult<List<FlightInfoResponseDTO>> queryReturnFlightWithFallback(QueryReturnFlightDTO queryReturnFlightDTO) {
        logger.info("启用往返程回程查询降级逻辑，使用单程接口查询");
        logger.info("降级查询参数: 出发机场={}, 到达机场={}, 回程日期={}, 成人数={}, 儿童数={}",
                queryReturnFlightDTO.getDeptAirport(), queryReturnFlightDTO.getArrAirport(),
                queryReturnFlightDTO.getReturnDate(),
                queryReturnFlightDTO.getAdultNum(), queryReturnFlightDTO.getChildNum());

        try {
            // 构建回程查询参数
            QueryFlightInfoDTO returnQuery = new QueryFlightInfoDTO();

            // 智能设置机场和城市代码
            // 回程：出发地=原回程查询的出发地，到达地=原回程查询的到达地
            String deptAirportCode = queryReturnFlightDTO.getDeptAirport(); // 回程出发地
            String arrAirportCode = queryReturnFlightDTO.getArrAirport(); // 回程到达地

            // 判断传入的是城市代码还是机场代码，并相应设置
            if (isCityCode(deptAirportCode)) {
                logger.info("检测到回程出发地使用城市代码: {}", deptAirportCode);
                returnQuery.setDeptCityCode(deptAirportCode);
                returnQuery.setDeptAirport(""); // 让系统自动查找该城市的机场
            } else {
                logger.info("检测到回程出发地使用机场代码: {}", deptAirportCode);
                returnQuery.setDeptAirport(deptAirportCode);
                returnQuery.setDeptCityCode(""); // 机场代码时城市代码留空
            }

            if (isCityCode(arrAirportCode)) {
                logger.info("检测到回程到达地使用城市代码: {}", arrAirportCode);
                returnQuery.setArrCityCode(arrAirportCode);
                returnQuery.setArrAirport(""); // 让系统自动查找该城市的机场
            } else {
                logger.info("检测到回程到达地使用机场代码: {}", arrAirportCode);
                returnQuery.setArrAirport(arrAirportCode);
                returnQuery.setArrCityCode(""); // 机场代码时城市代码留空
            }

            returnQuery.setDeptDate(queryReturnFlightDTO.getReturnDate());
            returnQuery.setAdultNum(queryReturnFlightDTO.getAdultNum() != null ? queryReturnFlightDTO.getAdultNum() : "1");
            returnQuery.setChildNum(queryReturnFlightDTO.getChildNum() != null ? queryReturnFlightDTO.getChildNum() : "0");

            logger.info("回程降级查询参数: 出发城市={}, 出发机场={}, 到达城市={}, 到达机场={}, 日期={}",
                    returnQuery.getDeptCityCode(), returnQuery.getDeptAirport(),
                    returnQuery.getArrCityCode(), returnQuery.getArrAirport(), returnQuery.getDeptDate());

            // 查询回程航班
            BaseResult<List<FlightInfoResponseDTO>> returnResult = queryFlight(returnQuery);
            if (returnResult.isSuccess() && CollectionUtils.isNotEmpty(returnResult.getData())) {
                // 为回程航班添加标识（如果需要的话）
                List<FlightInfoResponseDTO> flightList = returnResult.getData();
                // 按时间排序
                sortByTime(flightList);
                logger.info("降级查询回程航班成功，获得{}条航班", flightList.size());
                return BaseResult.success(flightList);
            } else {
                String returnError = returnResult.isSuccess() ? "返回结果为空" : returnResult.getReturnMessage();
                logger.warn("降级查询回程航班失败: {}", returnError);
                return BaseResult.fail(ReturnCode.FAIL, "暂无可用回程航班");
            }

        } catch (Exception e) {
            logger.error("回程降级查询异常", e);
            return BaseResult.fail(ReturnCode.FAIL, "查询回程航班异常: " + e.getMessage());
        }
    }

    /**
     * 往返程去程航班查询参数校验
     */
    private void checkDepartureFlightRequestBean(QueryDepartureFlightDTO queryDepartureFlightDTO) {
        if (StringUtils.isBlank(queryDepartureFlightDTO.getDeptAirport())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "出发机场/城市代码不能为空");
        }
        if (StringUtils.isBlank(queryDepartureFlightDTO.getArrAirport())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "到达机场/城市代码不能为空");
        }
        if (StringUtils.isBlank(queryDepartureFlightDTO.getDeptDate())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "出发日期不能为空");
        }
    }

    /**
     * 根据代码获取机场列表（智能判断是城市代码还是机场代码）
     */
    private List<CityAirportEntity> getAirportListByCode(String code) {
        List<CityAirportEntity> airportList = new ArrayList<>();

        if (StringUtils.isBlank(code)) {
            return airportList;
        }

        // 首先尝试作为城市代码查询
        List<CityAirportEntity> cityAirports = cityAirportService.queryAirportByCityCode(code);
        if (CollectionUtils.isNotEmpty(cityAirports)) {
            logger.info("根据城市代码{}查询到{}个机场", code, cityAirports.size());
            airportList.addAll(cityAirports);
        } else {
            // 如果城市代码查询无结果，作为机场代码处理
            CityAirportEntity airportEntity = cityAirportService.queryByAirPort3Code(code);
            if (airportEntity != null) {
                logger.info("根据机场代码{}查询到机场{}", code, airportEntity.getAirportName());
                airportList.add(airportEntity);
            } else {
                // 如果都查不到，创建一个临时的机场实体（保持向后兼容）
                logger.warn("代码{}既不是有效的城市代码也不是有效的机场代码，作为机场代码处理", code);
                CityAirportEntity tempEntity = new CityAirportEntity();
                tempEntity.setAirport3Code(code);
                airportList.add(tempEntity);
            }
        }

        return airportList;
    }

    /**
     * 调用NDC往返程接口查询航班
     */
    private List<FlightInfoResponseDTO> queryNDCDepartureFlight(String deptAirportCode, String arrAirportCode,
                                                                QueryDepartureFlightDTO queryDepartureFlightDTO) {
        try {
            // 构建B2B请求参数
            QueryDepartureFlightRequestDTO requestDTO = new QueryDepartureFlightRequestDTO();

            // 设置基础信息
            String customerSign = getMerchantCustomerSign();
            requestDTO.setRequestNo(RedisIdUtils.getId());
            requestDTO.setCustomerSign(customerSign);
            requestDTO.setRequestCustomerNo(customerSign.replaceAll("YP", "")); // B2B系统要求去掉YP前缀
            requestDTO.setAir2Code("AIR_CZ_NDC2C"); // 南航NDC2C代码

            // 设置其他必传参数
            requestDTO.setJourneyType("RT"); // 往返程
            requestDTO.setTravelMark("D"); // 国内航线
            requestDTO.setCurrencyCode("CNY"); // 币种
            requestDTO.setLanguage("zh-CN"); // 语言
            requestDTO.setAdultNum(Integer.parseInt(queryDepartureFlightDTO.getAdultNum() != null ? queryDepartureFlightDTO.getAdultNum() : "1"));
            requestDTO.setChildNum(Integer.parseInt(queryDepartureFlightDTO.getChildNum() != null ? queryDepartureFlightDTO.getChildNum() : "0"));

            // 构建行程信息 - 往返程需要包含去程和回程两个行程
            List<QueryDepartureFlightRequestDTO.Journey> journeyList = new ArrayList<>();

            // 去程
            QueryDepartureFlightRequestDTO.Journey outboundJourney = new QueryDepartureFlightRequestDTO.Journey();
            outboundJourney.setDeptAirportCode(deptAirportCode);
            outboundJourney.setArrAirportCode(arrAirportCode);
            outboundJourney.setDeptDate(queryDepartureFlightDTO.getDeptDate());
            journeyList.add(outboundJourney);

            // 回程 - 往返程查询必须包含回程信息
            if (StringUtils.isNotBlank(queryDepartureFlightDTO.getReturnDate())) {
                QueryDepartureFlightRequestDTO.Journey returnJourney = new QueryDepartureFlightRequestDTO.Journey();
                returnJourney.setDeptAirportCode(arrAirportCode); // 回程出发地=去程目的地
                returnJourney.setArrAirportCode(deptAirportCode); // 回程目的地=去程出发地
                returnJourney.setDeptDate(queryDepartureFlightDTO.getReturnDate()); // 回程日期
                journeyList.add(returnJourney);
            }

            requestDTO.setJourneyList(journeyList);

            logger.debug("调用B2B queryDepartureFlightList，机场组合：{}->{}, 参数: {}",
                    deptAirportCode, arrAirportCode, JSONUtils.toJsonString(requestDTO));

            // 调用B2B服务
            QueryFlightResponseDTO response = getTicketFacade().queryDepartureFlightList(requestDTO);

            logger.debug("B2B queryDepartureFlightList响应，机场组合：{}->{}: {}",
                    deptAirportCode, arrAirportCode, JSONUtils.toJsonString(response));

            if (response != null && "SUCCESS".equals(response.getStatus()) &&
                    response.getJourney() != null &&
                    CollectionUtils.isNotEmpty(response.getJourney().getOfferList())) {
                // 转换响应数据为前端格式
                return convertB2BFlightResponse(response);
            } else {
                String errorMsg = response != null ? response.getRetMsg() : "NDC往返程接口无响应";
                logger.debug("NDC往返程接口查询无结果，机场组合：{}->{}, 原因: {}",
                        deptAirportCode, arrAirportCode, errorMsg);
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.warn("NDC往返程接口调用异常，机场组合：{}->{}, 错误: {}",
                    deptAirportCode, arrAirportCode, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 往返程回程航班查询参数校验
     */
    private void checkReturnFlightRequestBean(QueryReturnFlightDTO queryReturnFlightDTO) {
        if (StringUtils.isBlank(queryReturnFlightDTO.getDeptAirport())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "出发机场/城市代码不能为空");
        }
        if (StringUtils.isBlank(queryReturnFlightDTO.getArrAirport())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "到达机场/城市代码不能为空");
        }
        if (StringUtils.isBlank(queryReturnFlightDTO.getReturnDate())) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "回程日期不能为空");
        }
    }

    /**
     * 调用NDC往返程回程接口查询航班
     */
    private List<FlightInfoResponseDTO> queryNDCReturnFlight(String deptAirportCode, String arrAirportCode,
                                                             QueryReturnFlightDTO queryReturnFlightDTO) {
        try {
            // 构建B2B请求参数
            QueryReturnFlightRequestDTO requestDTO = new QueryReturnFlightRequestDTO();

            // 设置基础信息
            String customerSign = getMerchantCustomerSign();
            requestDTO.setRequestNo(RedisIdUtils.getId());
            requestDTO.setCustomerSign(customerSign);
            requestDTO.setRequestCustomerNo(customerSign.replaceAll("YP", "")); // B2B系统要求去掉YP前缀
            requestDTO.setAir2Code("AIR_CZ_NDC2C"); // 南航NDC2C代码

            // 设置其他必传参数
            requestDTO.setJourneyType("RT"); // 往返程
            requestDTO.setTravelMark("D"); // 国内航线
            requestDTO.setCurrencyCode("CNY"); // 币种
            requestDTO.setLanguage("zh-CN"); // 语言
            requestDTO.setAdultNum(Integer.parseInt(queryReturnFlightDTO.getAdultNum() != null ? queryReturnFlightDTO.getAdultNum() : "1"));
            requestDTO.setChildNum(Integer.parseInt(queryReturnFlightDTO.getChildNum() != null ? queryReturnFlightDTO.getChildNum() : "0"));

            // 转换去程选择信息（只添加有效的offer）
            List<QueryReturnFlightRequestDTO.Offer> offers = new ArrayList<>();
            for (QueryReturnFlightDTO.DepartureOffer departureOffer : queryReturnFlightDTO.getDepartureOffers()) {
                if (departureOffer != null &&
                        StringUtils.isNotBlank(departureOffer.getCabinPriceId()) &&
                        StringUtils.isNotBlank(departureOffer.getCabinPriceItemId())) {
                    QueryReturnFlightRequestDTO.Offer offer = new QueryReturnFlightRequestDTO.Offer();
                    offer.setCabinPriceId(departureOffer.getCabinPriceId());
                    offer.setCabinPriceItemId(departureOffer.getCabinPriceItemId());
                    offers.add(offer);
                    logger.debug("添加去程选择信息: cabinPriceId={}, cabinPriceItemId={}",
                            departureOffer.getCabinPriceId(), departureOffer.getCabinPriceItemId());
                } else {
                    logger.warn("跳过无效的去程选择信息: {}", JSONUtils.toJsonString(departureOffer));
                }
            }
            requestDTO.setOffer(offers);

            logger.debug("调用B2B queryReturnFlightList，机场组合：{}->{}, 参数: {}",
                    deptAirportCode, arrAirportCode, JSONUtils.toJsonString(requestDTO));

            // 调用B2B服务
            QueryFlightResponseDTO response = getTicketFacade().queryReturnFlightList(requestDTO);
            logger.debug("B2B queryReturnFlightList响应，机场组合：{}->{}: {}",
                    deptAirportCode, arrAirportCode, JSONUtils.toJsonString(response));

            if (response != null && "SUCCESS".equals(response.getStatus()) &&
                    response.getJourney() != null &&
                    CollectionUtils.isNotEmpty(response.getJourney().getOfferList())) {
                // 转换响应数据为前端格式
                return convertB2BFlightResponse(response);
            } else {
                String errorMsg = response != null ? response.getRetMsg() : "NDC往返程回程接口无响应";
                logger.debug("NDC往返程回程接口查询无结果，机场组合：{}->{}, 原因: {}",
                        deptAirportCode, arrAirportCode, errorMsg);
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.warn("NDC往返程回程接口调用异常，机场组合：{}->{}, 错误: {}",
                    deptAirportCode, arrAirportCode, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * NDC往返程去程查询任务类 - 支持多线程查询
     */
    @Slf4j
    private static class NDCDepartureFlightTask implements Callable<List<FlightInfoResponseDTO>> {
        private String deptAirportCode;
        private String arrAirportCode;
        private QueryDepartureFlightDTO queryDepartureFlightDTO;
        private FlightFacadeImpl flightFacade;

        @Override
        public List<FlightInfoResponseDTO> call() throws Exception {
            try {
                log.info("NDC往返程去程查询任务开始执行，机场组合：{}->{}", deptAirportCode, arrAirportCode);
                
                // 构建B2B请求参数
                QueryDepartureFlightRequestDTO requestDTO = new QueryDepartureFlightRequestDTO();
                
                // 设置基础信息
                String customerSign = flightFacade.getMerchantCustomerSign();
                requestDTO.setRequestNo(RedisIdUtils.getId());
                requestDTO.setCustomerSign(customerSign);
                requestDTO.setRequestCustomerNo(customerSign.replaceAll("YP", "")); // B2B系统要求去掉YP前缀
                requestDTO.setAir2Code("AIR_CZ_NDC2C"); // 南航NDC2C代码
                
                // 设置其他必传参数
                requestDTO.setJourneyType("RT"); // 往返程
                requestDTO.setTravelMark("D"); // 国内航线
                requestDTO.setCurrencyCode("CNY"); // 币种
                requestDTO.setLanguage("zh-CN"); // 语言
                requestDTO.setAdultNum(Integer.parseInt(queryDepartureFlightDTO.getAdultNum() != null ? queryDepartureFlightDTO.getAdultNum() : "1"));
                requestDTO.setChildNum(Integer.parseInt(queryDepartureFlightDTO.getChildNum() != null ? queryDepartureFlightDTO.getChildNum() : "0"));
                
                // 构建行程信息 - 往返程需要包含去程和回程两个行程
                List<QueryDepartureFlightRequestDTO.Journey> journeyList = new ArrayList<>();
                
                // 去程
                QueryDepartureFlightRequestDTO.Journey outboundJourney = new QueryDepartureFlightRequestDTO.Journey();
                outboundJourney.setDeptAirportCode(deptAirportCode);
                outboundJourney.setArrAirportCode(arrAirportCode);
                outboundJourney.setDeptDate(queryDepartureFlightDTO.getDeptDate());
                journeyList.add(outboundJourney);
                
                // 回程 - 往返程查询必须包含回程信息
                if (StringUtils.isNotBlank(queryDepartureFlightDTO.getReturnDate())) {
                    QueryDepartureFlightRequestDTO.Journey returnJourney = new QueryDepartureFlightRequestDTO.Journey();
                    returnJourney.setDeptAirportCode(arrAirportCode); // 回程出发地=去程目的地
                    returnJourney.setArrAirportCode(deptAirportCode); // 回程目的地=去程出发地
                    returnJourney.setDeptDate(queryDepartureFlightDTO.getReturnDate()); // 回程日期
                    journeyList.add(returnJourney);
                }
                
                requestDTO.setJourneyList(journeyList);
                
                log.debug("调用B2B queryDepartureFlightList，机场组合：{}->{}, 参数: {}", 
                        deptAirportCode, arrAirportCode, JSONUtils.toJsonString(requestDTO));
                
                // 直接调用TicketFacade接口
                TicketFacade ticketFacade = flightFacade.getTicketFacade();
                QueryFlightResponseDTO response = ticketFacade.queryDepartureFlightList(requestDTO);
                
                log.debug("B2B queryDepartureFlightList响应，机场组合：{}->{}: {}",
                        deptAirportCode, arrAirportCode, JSONUtils.toJsonString(response));
                
                if (response != null && "SUCCESS".equals(response.getStatus()) && 
                    response.getJourney() != null && 
                    CollectionUtils.isNotEmpty(response.getJourney().getOfferList())) {
                    // 转换响应数据为前端格式
                    List<FlightInfoResponseDTO> result = flightFacade.convertB2BFlightResponse(response);
                    log.info("NDC往返程去程查询任务执行成功，机场组合：{}->{}, 获得{}条航班", 
                            deptAirportCode, arrAirportCode, result.size());
                    return result;
                } else {
                    String errorMsg = response != null ? response.getRetMsg() : "NDC往返程接口无响应";
                    log.debug("NDC往返程接口查询无结果，机场组合：{}->{}, 原因: {}", 
                            deptAirportCode, arrAirportCode, errorMsg);
                    return new ArrayList<>();
                }
                
            } catch (Exception e) {
                log.warn("NDC往返程接口调用异常，机场组合：{}->{}, 错误: {}", 
                        deptAirportCode, arrAirportCode, e.getMessage());
                return new ArrayList<>();
            }
        }

        public void setDeptAirportCode(String deptAirportCode) {
            this.deptAirportCode = deptAirportCode;
        }

        public void setArrAirportCode(String arrAirportCode) {
            this.arrAirportCode = arrAirportCode;
        }

        public void setQueryDepartureFlightDTO(QueryDepartureFlightDTO queryDepartureFlightDTO) {
            this.queryDepartureFlightDTO = queryDepartureFlightDTO;
        }

        public void setFlightFacade(FlightFacadeImpl flightFacade) {
            this.flightFacade = flightFacade;
        }
    }

    /**
     * NDC往返程回程查询任务类 - 支持多线程查询
     */
    @Slf4j
    private static class NDCReturnFlightTask implements Callable<List<FlightInfoResponseDTO>> {
        private String deptAirportCode;
        private String arrAirportCode;
        private QueryReturnFlightDTO queryReturnFlightDTO;
        private FlightFacadeImpl flightFacade;

        @Override
        public List<FlightInfoResponseDTO> call() throws Exception {
            try {
                log.info("NDC往返程回程查询任务开始执行，机场组合：{}->{}", deptAirportCode, arrAirportCode);
                
                // 构建B2B请求参数
                QueryReturnFlightRequestDTO requestDTO = new QueryReturnFlightRequestDTO();
                
                // 设置基础信息
                String customerSign = flightFacade.getMerchantCustomerSign();
                requestDTO.setRequestNo(RedisIdUtils.getId());
                requestDTO.setCustomerSign(customerSign);
                requestDTO.setRequestCustomerNo(customerSign.replaceAll("YP", "")); // B2B系统要求去掉YP前缀
                requestDTO.setAir2Code("AIR_CZ_NDC2C"); // 南航NDC2C代码
                
                // 设置其他必传参数
                requestDTO.setJourneyType("RT"); // 往返程
                requestDTO.setTravelMark("D"); // 国内航线
                requestDTO.setCurrencyCode("CNY"); // 币种
                requestDTO.setLanguage("zh-CN"); // 语言
                requestDTO.setAdultNum(Integer.parseInt(queryReturnFlightDTO.getAdultNum() != null ? queryReturnFlightDTO.getAdultNum() : "1"));
                requestDTO.setChildNum(Integer.parseInt(queryReturnFlightDTO.getChildNum() != null ? queryReturnFlightDTO.getChildNum() : "0"));
                
                // 转换去程选择信息（只添加有效的offer）
                List<QueryReturnFlightRequestDTO.Offer> offers = new ArrayList<>();
                for (QueryReturnFlightDTO.DepartureOffer departureOffer : queryReturnFlightDTO.getDepartureOffers()) {
                    if (departureOffer != null && 
                        StringUtils.isNotBlank(departureOffer.getCabinPriceId()) && 
                        StringUtils.isNotBlank(departureOffer.getCabinPriceItemId())) {
                        QueryReturnFlightRequestDTO.Offer offer = new QueryReturnFlightRequestDTO.Offer();
                        offer.setCabinPriceId(departureOffer.getCabinPriceId());
                        offer.setCabinPriceItemId(departureOffer.getCabinPriceItemId());
                        offers.add(offer);
                        log.debug("添加去程选择信息: cabinPriceId={}, cabinPriceItemId={}", 
                                departureOffer.getCabinPriceId(), departureOffer.getCabinPriceItemId());
                    } else {
                        log.warn("跳过无效的去程选择信息: {}", JSONUtils.toJsonString(departureOffer));
                    }
                }
                requestDTO.setOffer(offers);
                
                log.debug("调用B2B queryReturnFlightList，机场组合：{}->{}, 参数: {}", 
                        deptAirportCode, arrAirportCode, JSONUtils.toJsonString(requestDTO));
                
                // 直接调用TicketFacade接口
                TicketFacade ticketFacade = flightFacade.getTicketFacade();
                QueryFlightResponseDTO response = ticketFacade.queryReturnFlightList(requestDTO);
                log.debug("B2B queryReturnFlightList响应，机场组合：{}->{}: {}", 
                        deptAirportCode, arrAirportCode, JSONUtils.toJsonString(response));
                
                if (response != null && "SUCCESS".equals(response.getStatus()) && 
                    response.getJourney() != null && 
                    CollectionUtils.isNotEmpty(response.getJourney().getOfferList())) {
                    // 转换响应数据为前端格式
                    List<FlightInfoResponseDTO> result = flightFacade.convertB2BFlightResponse(response);
                    log.info("NDC往返程回程查询任务执行成功，机场组合：{}->{}, 获得{}条航班", 
                            deptAirportCode, arrAirportCode, result.size());
                    return result;
                } else {
                    String errorMsg = response != null ? response.getRetMsg() : "NDC往返程回程接口无响应";
                    log.debug("NDC往返程回程接口查询无结果，机场组合：{}->{}, 原因: {}", 
                            deptAirportCode, arrAirportCode, errorMsg);
                    return new ArrayList<>();
                }
                
            } catch (Exception e) {
                log.warn("NDC往返程回程接口调用异常，机场组合：{}->{}, 错误: {}", 
                        deptAirportCode, arrAirportCode, e.getMessage());
                return new ArrayList<>();
            }
        }

        public void setDeptAirportCode(String deptAirportCode) {
            this.deptAirportCode = deptAirportCode;
        }

        public void setArrAirportCode(String arrAirportCode) {
            this.arrAirportCode = arrAirportCode;
        }

        public void setQueryReturnFlightDTO(QueryReturnFlightDTO queryReturnFlightDTO) {
            this.queryReturnFlightDTO = queryReturnFlightDTO;
        }

        public void setFlightFacade(FlightFacadeImpl flightFacade) {
            this.flightFacade = flightFacade;
        }
    }

}

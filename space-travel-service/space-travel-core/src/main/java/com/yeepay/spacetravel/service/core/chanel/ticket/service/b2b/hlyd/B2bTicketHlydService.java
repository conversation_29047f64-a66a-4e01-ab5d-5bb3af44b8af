package com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.hlyd;

import com.yeepay.g3.bc.common.util.RedisIdUtil;
import com.yeepay.g3.facade.air.b2b.param.PayInfo;
import com.yeepay.g3.facade.air.b2b.param.PayInfosDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.CheckFlightPriceRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.CreateOrderRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.OrderPayRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.QueryFlightRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.request.QueryOrderRequestDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.CheckFlightPriceResponseDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.CreateOrderResponseDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.OrderPayResponseDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.QueryFlightResponseDTO;
import com.yeepay.g3.facade.air.ticket.dto.response.QueryOrderResponseDTO;
import com.yeepay.g3.facade.air.ticket.facade.TicketFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.env.IpServerStatisticUtils;
import com.yeepay.spacetravel.common.util.exception.OperationException;
import com.yeepay.spacetravel.common.util.redis.RedisUtils;
import com.yeepay.spacetravel.service.core.bean.flight.QueryFlightInfoBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.Policy;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPassagerBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPayResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.hlyd.dto.B2BOrderStatusEnum;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.hlyd.dto.PromotionInfo;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.AbstractB2bTicketService;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketFactory;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketService;
import com.yeepay.spacetravel.service.core.entity.RequestInfoEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.service.RequestInfoService;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderContactRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderFlightRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderPassengerRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.enums.common.SpaceTravelCommonStatusEnums;
import com.yeepay.spacetravel.service.facade.enums.passager.PassengerTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketMethodEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.TicketServiceEnums;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/30 上午11:05
 */
@Service
public class B2bTicketHlydService extends AbstractB2bTicketService implements B2bTicketService {

    private Logger logger = LoggerFactory.getLogger(B2bTicketHlydService.class);

    @Autowired
    private RequestInfoService requestInfoService;

    B2bTicketHlydService() {
        B2bTicketFactory.register("AIR_SC_NDC2C", this);
        B2bTicketFactory.register("AIR_CZ_NDC2C", this);
        B2bTicketFactory.register("AIR_SC_NDC", this);
        B2bTicketFactory.register("AIR_MF_NDC", this);
    }

    @Override
    public List<Policy> queryRealPrice(QueryFlightInfoBean bean) {

        logger.info("HLYD通道获取航班请求进入：{}", JSONUtils.toJsonString(bean));

        QueryFlightRequestDTO queryFlightRequestDTO = getQueryFlightRequestDTO(bean);
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(bean.getRequestChannelNo(), RequestTicketMethodEnums.QUERY_FLIGHT.name(), getChannel(), queryFlightRequestDTO, SpaceTravelCommonStatusEnums.INIT.name());

        try {
            TicketFacade ticketFacade = RemoteServiceFactory.getService(TicketFacade.class);

            logger.info("请求航旅易达获取政策入参：{}", JSONUtils.toJsonString(queryFlightRequestDTO));
            QueryFlightResponseDTO queryFlightResponseDTO = ticketFacade.queryFlightList(queryFlightRequestDTO);
            logger.info("请求航旅易达获取政策返回：{}", JSONUtils.toJsonString(queryFlightResponseDTO));

            //返回结果解析
            if (null == queryFlightResponseDTO || !"SUCCESS".equals(queryFlightResponseDTO.getStatus())) {
                logger.info("请求航旅易达航班查询未返回结果或失败");
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), queryFlightResponseDTO, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), JSONUtils.toJsonString(queryFlightResponseDTO), SpaceTravelCommonStatusEnums.SUCCESS.name());

            List<Policy> policyList = getPolicies(bean, queryFlightResponseDTO);
            return policyList;

        } catch (Exception e) {
            logger.info("请求航旅易达获取航班信息未知异常：", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }


        return null;
    }

    @NotNull
    private List<Policy> getPolicies(QueryFlightInfoBean bean, QueryFlightResponseDTO queryFlightResponseDTO) {

        HashMap<Object, Object> promotionMap = ConfigUtils.getDefaultValue("SPACE_TRAVEL_PROMOTION_CONFIG", new HashMap<>());

        String deptDate = bean.getDeptDate();
        List<Policy> policyList = new ArrayList<>();
        List<QueryFlightResponseDTO.Offer> offerList = queryFlightResponseDTO.getJourney().getOfferList();
        for (QueryFlightResponseDTO.Offer offer : offerList) {

            if (offer.getSegmentList().size() > 1) {
                continue;
            }

            QueryFlightResponseDTO.Segment segment = offer.getSegmentList().get(0);

            if (segment.getStop()) {
                continue;
            }

            String deptAirportCode = segment.getDeptAirportCode();
            String arrAirportCode = segment.getArrAirportCode();
            String deptDateTime = segment.getDeptDateTime().substring(0, 19);;
            String arrDateTime = segment.getArrDateTime().substring(0, 19);;
            String arrTerminal = segment.getArrTerminal();
            String depTerminal = segment.getDepTerminal();

            if ("--".equals(depTerminal)) {
                depTerminal = "";
            }
            if ("--".equals(arrTerminal)) {
                arrTerminal = "";
            }

            String flightNo = segment.getFlightNo();
            String meal = segment.getMeal();
            String planeType = segment.getPlaneType();
            Boolean share = segment.getShare();
            String operateFlightNo = segment.getOperateFlightNo();

            List<QueryFlightResponseDTO.CabinPrice> cabinPriceList = offer.getCabinPriceList();
            for (QueryFlightResponseDTO.CabinPrice cabinPrice : cabinPriceList) {

                List<QueryFlightResponseDTO.CabinPriceItem> cabinPriceItemList = cabinPrice.getCabinPriceItemList();

                Policy policy = new Policy();

                String cabinPriceId = RedisIdUtil.getId();
                RedisUtils.setCacheCoverOldValue(cabinPriceId,cabinPrice.getCabinPriceId(),1000 * 60 * 60);
                policy.setCabinPriceId(cabinPriceId);
                policy.setDeptAirport(deptAirportCode);
                policy.setDeptTime(deptDateTime);
                policy.setAircode(bean.getAirCode());
                policy.setPlaneType(planeType);
                policy.setMeal(meal);
                policy.setFlightNo(flightNo);
                policy.setProductCode("");
                policy.setStop("");
                policy.setArrAirport(arrAirportCode);
                policy.setArrTerminal(arrTerminal);
                policy.setDepTerminal(depTerminal);
                policy.setArrTime(arrDateTime);
                policy.setDeptDate(deptDate);
                //policy.setRuleDesc(cabinPriceItem.getRefundChangeRule());
                policy.setShare(share);
                policy.setOperateFlightNo(operateFlightNo);

                buildAdultPriceInfo(promotionMap, cabinPriceList, cabinPriceItemList, policy);
                buildChildPriceInfo(cabinPriceItemList, policy);

                policyList.add(policy);
            }

        }
        return policyList;
    }

    private void buildAdultPriceInfo(HashMap<Object, Object> promotionMap, List<QueryFlightResponseDTO.CabinPrice> cabinPriceList, List<QueryFlightResponseDTO.CabinPriceItem> cabinPriceItemList, Policy policy) {
        QueryFlightResponseDTO.CabinPriceItem adultCabinPriceItem = cabinPriceItemList
                .stream()
                .filter(m -> PassengerTypeEnums.ADULT.name().equals(m.getPassengerType()))
                .collect(Collectors.toList())
                .get(0);
        BigDecimal fuelTax = adultCabinPriceItem.getFuelTax();
        BigDecimal constructionFee = adultCabinPriceItem.getConstructionFee();
        policy.setTaxFeeAdult(fuelTax.add(constructionFee).toString());
        policy.setOilFeeAdult(fuelTax.toString());
        BigDecimal ticketPrice = adultCabinPriceItem.getTicketPrice();
        policy.setPriceAdult(ticketPrice);
        policy.setAmtAdultAirPortFee(constructionFee.toString());
        String cabinCode = adultCabinPriceItem.getCabinInfoList().get(0).getCabinCode();
        policy.setCabinCls(cabinCode);

        String cabinPriceItemId = RedisIdUtil.getId();
        RedisUtils.setCacheCoverOldValue(cabinPriceItemId,adultCabinPriceItem.getCabinPriceItemId(),1000 * 60 * 60);
        policy.setAdultCabinPriceItemId(cabinPriceItemId);

        String productCode = adultCabinPriceItem.getProductType();
        if (StringUtils.isNotBlank(productCode)) {
            policy.setProductCode(productCode);
            PromotionInfo promotionInfo = getPromotionInfo(promotionMap, productCode);
            if (!CheckUtils.isEmpty(promotionInfo)) {
                policy.setProductType(promotionInfo.getProductName());
                policy.setProductDesc(promotionInfo.getProductDesc());
            }

            QueryFlightResponseDTO.CabinPriceItem cabinPriceItem = getCabinPriceItem(cabinCode, PassengerTypeEnums.ADULT.name(), ticketPrice, cabinPriceList);
            if (!CheckUtils.isEmpty(cabinPriceItem)) {
                policy.setAdultOrigPrice(cabinPriceItem.getTicketPrice());
                policy.setCouponAmount(cabinPriceItem.getTicketPrice().subtract(ticketPrice));
            }

        }
    }

    private void buildChildPriceInfo(List<QueryFlightResponseDTO.CabinPriceItem> cabinPriceItemList, Policy policy) {
        List<QueryFlightResponseDTO.CabinPriceItem> childCabinPriceItemList = cabinPriceItemList
                .stream()
                .filter(m -> PassengerTypeEnums.CHILD.name().equals(m.getPassengerType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(childCabinPriceItemList)) {
            QueryFlightResponseDTO.CabinPriceItem childCabinPriceItem = childCabinPriceItemList.get(0);

            BigDecimal fuelTax = childCabinPriceItem.getFuelTax();
            if (null == fuelTax) {
                fuelTax = BigDecimal.ZERO;
            }

            BigDecimal constructionFee = childCabinPriceItem.getConstructionFee();
            if (null == constructionFee) {
                constructionFee = BigDecimal.ZERO;
            }

            policy.setOilFeeChild(fuelTax);
            policy.setTaxFeeChild(fuelTax.add(constructionFee));
            policy.setPriceChild(childCabinPriceItem.getTicketPrice());
            policy.setAmtChildAirPortFee(constructionFee);

            String cabinPriceItemId = RedisIdUtil.getId();
            RedisUtils.setCacheCoverOldValue(cabinPriceItemId,childCabinPriceItem.getCabinPriceItemId(),1000 * 60 * 60);
            policy.setChildCabinPriceItemId(cabinPriceItemId);

        }
    }

    private QueryFlightResponseDTO.CabinPriceItem getCabinPriceItem(String cabinCode,
                                                                    String passengerType,
                                                                    BigDecimal ticketPrice,
                                                                    List<QueryFlightResponseDTO.CabinPrice> cabinPriceList) {

        for (QueryFlightResponseDTO.CabinPrice cabinPrice : cabinPriceList) {

            List<QueryFlightResponseDTO.CabinPriceItem> cabinPriceItems = cabinPrice.getCabinPriceItemList()
                    .stream()
                    .filter(m -> m.getPassengerType().equals(passengerType) &&
                            cabinCode.equals(m.getCabinInfoList().get(0).getCabinCode()) &&
                            m.getTicketPrice().compareTo(ticketPrice) > 0 &&
                            StringUtils.isBlank(m.getProductType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cabinPriceItems)) {
                cabinPriceItems.sort((o1, o2) -> o2.getTicketPrice().compareTo(o1.getTicketPrice()));
                return cabinPriceItems.get(0);
            }

        }

        return null;

    }


    private PromotionInfo getPromotionInfo(HashMap<Object, Object> promotionMap,String productCode) {

        Object jsonStr = promotionMap.get(productCode);
        if (CheckUtils.isEmpty(jsonStr)){
            return null;
        } else {
            PromotionInfo promotionInfo = JSONUtils.jsonToBean((String)jsonStr, PromotionInfo.class);
            return promotionInfo;
        }

    }



    @NotNull
    private QueryFlightRequestDTO getQueryFlightRequestDTO(QueryFlightInfoBean bean) {
        QueryFlightRequestDTO queryFlightRequestDTO = new QueryFlightRequestDTO();

        QueryFlightRequestDTO.Journey journey = new QueryFlightRequestDTO.Journey();
        journey.setDeptAirportCode(bean.getDeptAirport());
        journey.setArrAirportCode(bean.getArrAirport());
        journey.setDeptDate(bean.getDeptDate());
        List<QueryFlightRequestDTO.Journey> journeyList = new ArrayList<>();
        journeyList.add(journey);

        queryFlightRequestDTO.setJourneyList(journeyList);
        queryFlightRequestDTO.setCustomerSign(getMerchantCustomerSign());
        queryFlightRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        queryFlightRequestDTO.setRequestNo(bean.getRequestChannelNo());
        queryFlightRequestDTO.setAir2Code(bean.getAirCode());
        queryFlightRequestDTO.setJourneyType("OW");
        queryFlightRequestDTO.setTravelMark("D");

        int adultNum = 1;
        if (StringUtils.isNotBlank(bean.getAdultNum()) && !"0".equals(bean.getAdultNum())){
            adultNum = Integer.parseInt(bean.getAdultNum());
        }
        int childNum = 0;
        if (StringUtils.isNotBlank(bean.getChildNum()) && !"0".equals(bean.getChildNum())){
            childNum = Integer.parseInt(bean.getChildNum());
        }

        queryFlightRequestDTO.setAdultNum(adultNum);
        queryFlightRequestDTO.setChildNum(childNum);
        return queryFlightRequestDTO;
    }

    private Map<String, String> getRequestB2bInfo() {
        Map<String, String> info = new HashMap<>();
        logger.info("当前处理逻辑所在环境是否为内测{}", IpServerStatisticUtils.getEnvironmentTest());
        if (IpServerStatisticUtils.getEnvironmentTest()) {
            info = ConfigUtils.requestB2bMerchantInfoForNc(getChannel());
        } else {
            info = ConfigUtils.requestB2bMerchantInfo(getChannel());
        }
        return info;
    }

    private String getChannel() {
        return TicketServiceEnums.AIRB2B.name();
    }

    private String getMerchantCustomerSign() {
        return getRequestB2bInfo().get("customerSign");
    }

    private String getMpPayAccount() {
        return getRequestB2bInfo().get("mpPayAccount");
    }

    private String getMpPayAccountPwd() {
        return getRequestB2bInfo().get("mpPayAccountPwd");
    }


    @Override
    public TicketOrderResponseBean order(TicketOrderRequestDTO dto, String orderId, String requestChannelNo) {

        logger.info("HLYD通道创建订单请求进入：{},{},{}", JSONUtils.toJsonString(dto), orderId, requestChannelNo);

        TicketOrderResponseBean ticketOrderResponseBean = new TicketOrderResponseBean();
        // 先请求获取政策

        try {

            TicketOrderFlightRequestDTO flight = dto.getFlight();
            TicketFacade ticketFacade = RemoteServiceFactory.getService(TicketFacade.class);

            // 请求验价
            CheckFlightPriceResponseDTO.CabinPrice cabinPrice = requestCheckFlightPrice(dto, ticketFacade,requestChannelNo);

            // 请求创建订单
            CreateOrderResponseDTO order = requestCreateOrder(dto, orderId, flight, ticketFacade, cabinPrice, requestChannelNo);

            ticketOrderResponseBean.setOrderStatus("ORDER_SUCCESS");
            ticketOrderResponseBean.setPayPrice(order.getOrderAmount());
            ticketOrderResponseBean.setAirOrderNo(order.getOrderId());
            ticketOrderResponseBean.setOrderMsg(order.getRetMsg());

            String payTimeLimit = order.getPayTimeLimit();
            if (StringUtils.isNotBlank(payTimeLimit)) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ticketOrderResponseBean.setPayTimeLimit(format.parse(payTimeLimit));
            }

            return ticketOrderResponseBean;

        } catch (OperationException e) {
            logger.error("航旅易达请求创单流程OperationException异常：", e);
            ticketOrderResponseBean.setOrderMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("航旅易达请求创单流程异常：", e);
            ticketOrderResponseBean.setOrderMsg("创建订单失败");
        }
        return ticketOrderResponseBean;
    }

    @NotNull
    private CreateOrderResponseDTO requestCreateOrder(TicketOrderRequestDTO dto,
                                                      String orderId,
                                                      TicketOrderFlightRequestDTO flight,
                                                      TicketFacade ticketFacade,
                                                      CheckFlightPriceResponseDTO.CabinPrice cabinPrice,
                                                      String requestChannelNo) {

        CreateOrderRequestDTO createOrderRequestDTO = buildCreateOrderRequestDTO(dto, flight, cabinPrice, requestChannelNo);
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(orderId, RequestTicketMethodEnums.ORDER.name(), getChannel(), JSONUtils.toJsonString(createOrderRequestDTO), SpaceTravelCommonStatusEnums.INIT.name());

        // 请求创建订单
        logger.info("请求航旅易达创建订单入参：{}", JSONUtils.toJsonString(createOrderRequestDTO));
        CreateOrderResponseDTO order = ticketFacade.createOrder(createOrderRequestDTO);
        logger.info("请求航旅易达创建订单返回：{}", JSONUtils.toJsonString(order));

        if (null == order || !"CREATE_ORDER_SUCCESS".equals(order.getStatus())) {
            logger.info("请求航旅易达创建订单未返回结果或失败");
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), JSONUtils.toJsonString(order), SpaceTravelCommonStatusEnums.SUCCESS.name());
            String retMsg = order.getRetMsg();
            if (StringUtils.isBlank(retMsg)) {
                retMsg = "创建订单失败";
            }
            throw OperationException.newInstantce("999999", retMsg);
        }
        requestInfoService.updateRequestInfo(requestInfoEntity.getId(), JSONUtils.toJsonString(order), SpaceTravelCommonStatusEnums.SUCCESS.name());
        return order;
    }

    @Nullable
    private CheckFlightPriceResponseDTO.CabinPrice requestCheckFlightPrice(TicketOrderRequestDTO dto,
                                                                           TicketFacade ticketFacade,
                                                                           String requestChannelNo) {
        CheckFlightPriceRequestDTO checkFlightPriceRequestDTO = new CheckFlightPriceRequestDTO();

        String cabinPriceId = RedisUtils.getTargetFromRedis(dto.getFlight().getCabinPriceId(),String.class);
        String adultCabinPriceItemId = RedisUtils.getTargetFromRedis(dto.getFlight().getAdultCabinPriceItemId(),String.class);
        String childCabinPriceItemId = RedisUtils.getTargetFromRedis(dto.getFlight().getChildCabinPriceItemId(),String.class);

        if (StringUtils.isBlank(cabinPriceId) || StringUtils.isBlank(adultCabinPriceItemId)) {
            throw OperationException.newInstantce("999999", "创建订单失败，请重新获取航班");
        }

        List<CheckFlightPriceRequestDTO.Offer> checkOfferList = new ArrayList<>();
        CheckFlightPriceRequestDTO.Offer adultCheckOffer = new CheckFlightPriceRequestDTO.Offer();
        adultCheckOffer.setCabinPriceId(cabinPriceId);
        adultCheckOffer.setCabinPriceItemId(adultCabinPriceItemId);
        checkOfferList.add(adultCheckOffer);

        if (StringUtils.isNotBlank(childCabinPriceItemId)) {
            CheckFlightPriceRequestDTO.Offer childCheckOffer = new CheckFlightPriceRequestDTO.Offer();
            childCheckOffer.setCabinPriceId(cabinPriceId);
            childCheckOffer.setCabinPriceItemId(childCabinPriceItemId);
            checkOfferList.add(childCheckOffer);
        }

        checkFlightPriceRequestDTO.setOffer(checkOfferList);
        checkFlightPriceRequestDTO.setCustomerSign(getMerchantCustomerSign());
        checkFlightPriceRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        checkFlightPriceRequestDTO.setRequestNo(requestChannelNo);
        checkFlightPriceRequestDTO.setAir2Code(dto.getFlight().getAirCode());

        int adultNumber = dto.getPassagers()
                .stream()
                .filter(m -> PassengerTypeEnums.ADULT.name().equals(m.getPassengerType()))
                .collect(Collectors.toList())
                .size();

        int childNumber = dto.getPassagers()
                .stream()
                .filter(m -> PassengerTypeEnums.CHILD.name().equals(m.getPassengerType()))
                .collect(Collectors.toList())
                .size();

        checkFlightPriceRequestDTO.setAdultNum(adultNumber);
        checkFlightPriceRequestDTO.setChildNum(childNumber);
        checkFlightPriceRequestDTO.setTravelMark("D");
        logger.info("请求航旅易达验价入参：{}", JSONUtils.toJsonString(checkFlightPriceRequestDTO));
        CheckFlightPriceResponseDTO checkFlightPriceResponseDTO = ticketFacade.checkFlightPrice(checkFlightPriceRequestDTO);
        logger.info("请求航旅易达验价返回：{}", JSONUtils.toJsonString(checkFlightPriceResponseDTO));

        if (null == checkFlightPriceResponseDTO || !"SUCCESS".equals(checkFlightPriceResponseDTO.getStatus()) ||
                checkFlightPriceResponseDTO.getOffer().getCabinPriceList().size() > 1) {
            logger.info("请求航旅易达验价未返回结果或失败");
            throw OperationException.newInstantce("999999", "创建订单失败，验价失败");
        }

        BigDecimal totalPrice = BigDecimal.ZERO;
        CheckFlightPriceResponseDTO.CabinPrice cabinPrice = checkFlightPriceResponseDTO.getOffer().getCabinPriceList().get(0);
        List<CheckFlightPriceResponseDTO.CabinPriceItem> cabinPriceItemList = cabinPrice.getCabinPriceItemList();
        for (CheckFlightPriceResponseDTO.CabinPriceItem cabinPriceItem : cabinPriceItemList) {
            if (PassengerTypeEnums.ADULT.name().equals(cabinPriceItem.getPassengerType())) {
                totalPrice = totalPrice.add(new BigDecimal(cabinPriceItem.getTotalPrice()).multiply(new BigDecimal(adultNumber)));
            } else if (PassengerTypeEnums.CHILD.name().equals(cabinPriceItem.getPassengerType())) {
                totalPrice = totalPrice.add(new BigDecimal(cabinPriceItem.getTotalPrice()).multiply(new BigDecimal(childNumber)));
            }
        }

        if (0 != totalPrice.compareTo(dto.getTicketTotalAmount())) {
            logger.info("价格不一致");
            throw OperationException.newInstantce("999999", "创建订单失败，验价失败，请重新获取政策信息");
        }

        return cabinPrice;
    }

    private boolean checkTime(String oldTime, String newTime) {

        // 定义日期时间格式
        DateTimeFormatter formatterWithoutNano = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析字符串为 LocalDateTime 对象
        LocalDateTime time1 = LocalDateTime.parse(newTime.substring(0, 19), formatterWithoutNano);
        LocalDateTime time2 = LocalDateTime.parse(oldTime.substring(0, 19), formatterWithoutNano);

        // 判断是否相等
        if (time1.isEqual(time2)) {
            logger.info("当前时间相等");
            return true;
        } else {
            logger.info("当前时间不相等");
            return false;
        }

    }


    @NotNull
    private CreateOrderRequestDTO buildCreateOrderRequestDTO(TicketOrderRequestDTO dto,
                                                             TicketOrderFlightRequestDTO flight,
                                                             CheckFlightPriceResponseDTO.CabinPrice cabinPrice,
                                                             String requestChannelNo) {

        BigDecimal totalPrice = new BigDecimal(flight.getTaxFeeAdult()).add(new BigDecimal(flight.getPriceAdult()));
        List<CreateOrderRequestDTO.Passenger> passengerList = getPassengers(dto, cabinPrice, totalPrice);
        CreateOrderRequestDTO createOrderRequestDTO = new CreateOrderRequestDTO();

        createOrderRequestDTO.setPassengerList(passengerList);

        TicketOrderContactRequestDTO ticketOrderContactRequestDTO = dto.getContact();
        List<CreateOrderRequestDTO.Contact> contactList = buildContacts(ticketOrderContactRequestDTO);

        createOrderRequestDTO.setContactList(contactList);
        createOrderRequestDTO.setCustomerSign(getMerchantCustomerSign());
        createOrderRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        createOrderRequestDTO.setRequestNo(requestChannelNo);
        createOrderRequestDTO.setAir2Code(dto.getFlight().getAirCode());
        createOrderRequestDTO.setJourneyType("OW");
        createOrderRequestDTO.setTravelMark("D");
        createOrderRequestDTO.setAdultNum(passengerList.size());
        createOrderRequestDTO.setChildNum(0);
        return createOrderRequestDTO;
    }

    @Nullable
    private QueryFlightResponseDTO requestQueryFlight(TicketOrderFlightRequestDTO flight, TicketFacade ticketFacade,
                                                      QueryFlightRequestDTO queryFlightRequestDTO,
                                                      TicketOrderRequestDTO dto) {
        QueryFlightRequestDTO.Journey journey = new QueryFlightRequestDTO.Journey();
        journey.setDeptAirportCode(flight.getDeptAirport());
        journey.setArrAirportCode(flight.getArrAirport());
        journey.setDeptDate(flight.getDeptDate());
        List<QueryFlightRequestDTO.Journey> journeyList = new ArrayList<>();
        journeyList.add(journey);

        queryFlightRequestDTO.setJourneyList(journeyList);
        queryFlightRequestDTO.setCustomerSign(getMerchantCustomerSign());
        queryFlightRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        queryFlightRequestDTO.setRequestNo("XLHY-" + RedisIdUtil.getId());
        queryFlightRequestDTO.setAir2Code("AIR_SC_NDC2C");
        queryFlightRequestDTO.setJourneyType("OW");
        queryFlightRequestDTO.setAdultNum(dto.getPassagers().size());
        queryFlightRequestDTO.setChildNum(0);
        logger.info("创单-请求航旅易达获取政策入参：{}", JSONUtils.toJsonString(queryFlightRequestDTO));
        QueryFlightResponseDTO queryFlightResponseDTO = ticketFacade.queryFlightList(queryFlightRequestDTO);
        logger.info("创单-请求航旅易达获取政策返回：{}", JSONUtils.toJsonString(queryFlightResponseDTO));

        //返回结果解析
        if (null == queryFlightResponseDTO || !"SUCCESS".equals(queryFlightResponseDTO.getStatus())) {
            logger.info("请求航旅易达航班查询未返回结果或失败");
            throw OperationException.newInstantce("999999", "创建订单失败，获取价格失败");
        }

        return queryFlightResponseDTO;
    }

    @NotNull
    private List<CreateOrderRequestDTO.Contact> buildContacts(TicketOrderContactRequestDTO ticketOrderContactRequestDTO) {
        List<CreateOrderRequestDTO.Contact> contactList = new ArrayList<>();
        CreateOrderRequestDTO.Contact contact = new CreateOrderRequestDTO.Contact();
        String contactName = ticketOrderContactRequestDTO.getContactName();
//        contact.setName(contactName);
        contact.setMobile(ticketOrderContactRequestDTO.getContactMobile());
        contact.setEmail("<EMAIL>");
        contact.setFirstName(contactName.substring(0, 1));
        contact.setLastName(contactName.substring(1));
        contactList.add(contact);
        return contactList;
    }

    @NotNull
    private List<CreateOrderRequestDTO.Passenger> getPassengers(TicketOrderRequestDTO dto,
                                                                CheckFlightPriceResponseDTO.CabinPrice cabinPrice,
                                                                BigDecimal totalPrice) {

        List<CreateOrderRequestDTO.Passenger> passengerList = new ArrayList<>();
        List<TicketOrderPassengerRequestDTO> requestDTOList = dto.getPassagers();
        for (TicketOrderPassengerRequestDTO ticketOrderPassengerRequestDTO : requestDTOList) {

            CreateOrderRequestDTO.Passenger passenger = new CreateOrderRequestDTO.Passenger();
            passenger.setFirstName(ticketOrderPassengerRequestDTO.getName().substring(0, 1));
            passenger.setLastName(ticketOrderPassengerRequestDTO.getName().substring(1));

            String passengerType = ticketOrderPassengerRequestDTO.getPassengerType();
            passenger.setType(passengerType);
            passenger.setCertType(ticketOrderPassengerRequestDTO.getCertType());
            passenger.setMobile(ticketOrderPassengerRequestDTO.getTel());
            passenger.setCertNo(ticketOrderPassengerRequestDTO.getCretNum());

            String formattedBirthDate = "";
            try {
                String birthDateStr = ticketOrderPassengerRequestDTO.getCretNum().substring(6, 14);
                SimpleDateFormat sdfInput = new SimpleDateFormat("yyyyMMdd");
                Date birthDate = sdfInput.parse(birthDateStr);
                SimpleDateFormat sdfOutput = new SimpleDateFormat("yyyy-MM-dd");
                formattedBirthDate = sdfOutput.format(birthDate);
            } catch (Exception e) {
                logger.error("解析日期异常：", e);
            }
            passenger.setBirthday(formattedBirthDate);
            passenger.setGender(ticketOrderPassengerRequestDTO.getSex());
            passenger.setCabinPriceId(cabinPrice.getCabinPriceId());

            String cabinPriceItemId = cabinPrice.getCabinPriceItemList()
                    .stream()
                    .filter(m -> passengerType.equals(m.getPassengerType()))
                    .collect(Collectors.toList())
                    .get(0)
                    .getCabinPriceItemId();
            passenger.setCabinPriceItemId(cabinPriceItemId);
            passenger.setTotalPrice(totalPrice.toString());

            passengerList.add(passenger);
        }
        return passengerList;
    }

    @Override
    public TicketPayResponseBean payTicket(String orderId,
                                           String requestTicketOrderId,
                                           BigDecimal payAmount,
                                           FlightInfoEntity flightInfo) {

        OrderPayRequestDTO orderPayRequestDTO = buildOrderPayRequestDTO(requestTicketOrderId, flightInfo);

        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(orderId, RequestTicketMethodEnums.PAY_TICKET.name(), getChannel(), orderPayRequestDTO, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            logger.info("请求HLYD支付参数为：{}", JSONUtils.toJsonString(orderPayRequestDTO));
            TicketFacade ticketFacade = RemoteServiceFactory.getService(TicketFacade.class);

            OrderPayResponseDTO orderPayResponseDTO = ticketFacade.orderPay(orderPayRequestDTO);
            logger.info("请求HLYD支付返回信息为：{}", JSONUtils.toJsonString(orderPayResponseDTO));
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), orderPayResponseDTO, SpaceTravelCommonStatusEnums.SUCCESS.name());

            if (CheckUtils.isEmpty(orderPayResponseDTO)) {
                return null;
            }

            String orderStatus = "INIT_PAY";
            TicketPayResponseBean responseBean = new TicketPayResponseBean();
            String status = orderPayResponseDTO.getStatus();
            if (B2BOrderStatusEnum.ORDERPAY_FAIL.name().equals(status)) {
                orderStatus = "PAY_FAIL";
            } else if (B2BOrderStatusEnum.ORDERPAY_SUCCESS.name().equals(status)) {
                responseBean.setPayRequestId(orderPayResponseDTO.getPayOrderNo());
                orderStatus = "PAY_SUCCESS";
            }
            responseBean.setOrderStatus(orderStatus);
            responseBean.setOrderMsg(orderPayResponseDTO.getRetMsg());
            logger.info("订单支付返回参数：{}",JSONUtils.toJsonString(responseBean));
            return responseBean;

        } catch (Exception e) {
            logger.error("请求HLYD支付出票出现未知异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }

        return null;
    }

    @NotNull
    private OrderPayRequestDTO buildOrderPayRequestDTO(String requestTicketOrderId, FlightInfoEntity flightInfo) {
        OrderPayRequestDTO orderPayRequestDTO = new OrderPayRequestDTO();
        orderPayRequestDTO.setCustomerSign(getMerchantCustomerSign());
        orderPayRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        orderPayRequestDTO.setRequestNo(requestTicketOrderId);
        orderPayRequestDTO.setAir2Code(flightInfo.getAirCode());
        orderPayRequestDTO.setTravelMark("D");

        List<PayInfosDTO> payInfosDTOList = new ArrayList<>();
        PayInfosDTO payInfosDTO = new PayInfosDTO();
        payInfosDTO.setIndex(1);
        payInfosDTO.setFrpId("M_P");
        PayInfo payInfo = new PayInfo();
        payInfo.setUserName(getMpPayAccount());
        payInfo.setPwd(getMpPayAccountPwd());
        payInfosDTO.setPayInfo(payInfo);
        payInfosDTOList.add(payInfosDTO);
        orderPayRequestDTO.setPayInfos(JSONUtils.toJsonString(payInfosDTOList));
        return orderPayRequestDTO;
    }

    @Override
    public TicketOrderResponseBean queryOrder(String orderId,String requestId,FlightInfoEntity flightInfo) {

        QueryOrderRequestDTO queryOrderRequestDTO = buildQueryOrderRequestDTO(requestId);

        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(requestId, RequestTicketMethodEnums.QUERY_ORDER.name(), getChannel(), queryOrderRequestDTO, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            TicketFacade ticketFacade = RemoteServiceFactory.getService(TicketFacade.class);
            logger.info("请求HLYD查询订单参数为：{}",JSONUtils.toJsonString(queryOrderRequestDTO));
            QueryOrderResponseDTO queryOrderResponseDTO = ticketFacade.queryOrder(queryOrderRequestDTO);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), queryOrderResponseDTO, SpaceTravelCommonStatusEnums.SUCCESS.name());
            logger.info("请求HLYD查询订单返回参数为：{}",JSONUtils.toJsonString(queryOrderResponseDTO));

            if (CheckUtils.isEmpty(queryOrderResponseDTO)){
                return null;
            }

            String status = queryOrderResponseDTO.getStatus();
            TicketOrderResponseBean responseBean = new TicketOrderResponseBean();
            responseBean.setOrderStatus(B2BOrderStatusEnum.getOrderStatusInfo(status));
            responseBean.setOrderMsg(queryOrderResponseDTO.getRetMsg());
            responseBean.setPayRequestId(queryOrderResponseDTO.getPayOrderNo());

            if (B2BOrderStatusEnum.TICKET_SUCCESS.name().equals(status)) {

                List<QueryOrderResponseDTO.Passenger> passengerList = queryOrderResponseDTO.getPassengerList();
                List<QueryOrderResponseDTO.TicketInfo> ticketInfoList = queryOrderResponseDTO.getTicketInfoList();
                Map<String,String> ticketNumberMap = new HashMap<>();
                for (QueryOrderResponseDTO.TicketInfo ticketInfo : ticketInfoList){
                    ticketNumberMap.put(ticketInfo.getPassengerId(),ticketInfo.getTicketNo());
                }

                List<TicketPassagerBean> resultPassengerList = new ArrayList<>();
                for (QueryOrderResponseDTO.Passenger passenger : passengerList){
                    TicketPassagerBean resultPassenger = new TicketPassagerBean();
                    resultPassenger.setCretNum(passenger.getCertNo());
                    resultPassenger.setTicketNo(ticketNumberMap.get(passenger.getPassengerId()));
                    resultPassengerList.add(resultPassenger);
                }
                responseBean.setPassenger(resultPassengerList);
            }

            logger.info("查询订单返回参数：{}",JSONUtils.toJsonString(responseBean));
            return responseBean;

        } catch (Exception e) {
            logger.error("请求自动出票订单查询接口出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;

    }

    @NotNull
    private QueryOrderRequestDTO buildQueryOrderRequestDTO(String requestId) {
        QueryOrderRequestDTO queryOrderRequestDTO = new QueryOrderRequestDTO();
        queryOrderRequestDTO.setCustomerSign(getMerchantCustomerSign());
        queryOrderRequestDTO.setRequestNo(requestId);
        queryOrderRequestDTO.setRequestCustomerNo(getMerchantCustomerSign().replaceAll("YP", ""));
        return queryOrderRequestDTO;
    }
}

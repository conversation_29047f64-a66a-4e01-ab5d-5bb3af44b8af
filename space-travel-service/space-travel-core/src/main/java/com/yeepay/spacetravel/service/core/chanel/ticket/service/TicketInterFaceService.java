package com.yeepay.spacetravel.service.core.chanel.ticket.service;

import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.encDec.AirB2bDigesHmactUtil;
import com.yeepay.spacetravel.common.util.encDec.AirB2bTokenEcryptUtils;
import com.yeepay.spacetravel.common.util.env.IpServerStatisticUtils;
import com.yeepay.spacetravel.common.util.http.HttpClient;
import com.yeepay.spacetravel.service.core.bean.flight.QueryFlightInfoBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.Policy;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.RealPriceRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderQueryRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPassagerBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPayResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketRefundOrderQueryRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketRefundRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketRefundResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketFactory;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketService;
import com.yeepay.spacetravel.service.core.entity.RequestInfoEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.entity.passenger.PassengerEntity;
import com.yeepay.spacetravel.service.core.entity.refund.RefundOrderEntity;
import com.yeepay.spacetravel.service.core.service.RequestInfoService;
import com.yeepay.spacetravel.service.core.service.flight.FlightInfoService;
import com.yeepay.spacetravel.service.core.service.refund.RefundOrderService;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.enums.common.SpaceTravelCommonStatusEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketMethodEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.TicketServiceEnums;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.http.NameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/08 17:08
 * @Description:
 */
@Service
public class TicketInterFaceService extends AbstractTicketBiz {
    private Logger logger = LoggerFactory.getLogger(TicketInterFaceService.class);
    private static final String airAccountPwd = "007yeepay";
    private static final String perfix = "##";
    //"https://airt.yeepay.com/air-ticket-api/airb2c/request.action";
    //测试地址：http://ycetest.yeepay.com:30564/air-ticket-api/airb2c/request.action
    //生产地址：https://airt.yeepay.com/air-ticket-api/airb2c/request.action
//    private static final String COMMON_URL = "http://ycetest.yeepay.com:30564/air-ticket-api/airb2c/request.action";

    private static final String QUREY_FLIGHT_SUCCESS_STATUS = "GETPRICE_SUCCESS";

    @Autowired
    private RequestInfoService requestInfoService;
    @Autowired
    private RefundOrderService refundOrderService;
    @Autowired
    private FlightInfoService flightInfoService;

    @Override
    public String getChannel() {
        return TicketServiceEnums.AIRB2B.name();
    }

    @Override
    public List<Policy> queryRealPrice(QueryFlightInfoBean bean) {

        B2bTicketService service = B2bTicketFactory.getService(bean.getAirCode());
        return service.queryRealPrice(bean);

    }

    @Override
    public TicketOrderResponseBean order(TicketOrderRequestDTO dto, String orderId, String requestChannelNo) {
        String airCode = null;
        if ("RT".equals(dto.getJourneyType())) {
            airCode = dto.getDeparture().getAirCode();
        }else {
            airCode = dto.getFlight().getAirCode();
        }
        B2bTicketService service = B2bTicketFactory.getService(airCode);
        return service.order(dto,orderId, requestChannelNo);

    }

    @Override
    public TicketPayResponseBean payTicket(String orderId, String requestTicketOrderId, BigDecimal payAmount) {

        FlightInfoEntity flightInfo = flightInfoService.queryByOrderId(orderId);
        B2bTicketService service = B2bTicketFactory.getService(flightInfo.getAirCode());
        return service.payTicket(orderId, requestTicketOrderId, payAmount, flightInfo);

    }

    @Override
    public TicketRefundResponseBean refundTicket(String orderId,String requestId, String refundRequestNo, List<PassengerEntity> passengers, String refundReason, ImmutablePair<String,String> file) {
        FlightInfoEntity flightInfo = flightInfoService.queryByOrderId(orderId);
        TicketRefundRequestBean bean = buildRefundBean(requestId, refundRequestNo, passengers, refundReason, file,flightInfo.getAirCode());
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(requestId, RequestTicketMethodEnums.REFUND_TICKET.name(), getChannel(), bean, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            List<NameValuePair> requestParm = buildParam(bean);
            logger.info("请求自动出票退票接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出票退票耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票退票接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票支付退票接口未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
            logger.info("请求航司支付成功");
            TicketRefundResponseBean responseBean = JSONUtils.jsonToBean(responseData, TicketRefundResponseBean.class);
            return responseBean;

        } catch (Exception e) {
            logger.error("请求自动出票退票出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;
    }

    @Override
    public TicketOrderResponseBean queryOrder(String orderId,String requestId) {

        logger.info("请求自动出票查单的参数为{}", requestId);
        FlightInfoEntity flightInfo = flightInfoService.queryByOrderId(orderId);

        B2bTicketService service = B2bTicketFactory.getService(flightInfo.getAirCode());
        return service.queryOrder(orderId, requestId, flightInfo);
    }

    @Override
    public TicketRefundResponseBean queryRefundOrder(String refundOrderId,String refundRequestNo) {
        RefundOrderEntity refundOrder = refundOrderService.get(refundOrderId);
        FlightInfoEntity flightInfo = flightInfoService.queryByOrderId(refundOrder.getOrderId());
        TicketRefundOrderQueryRequestBean bean = buildRefundOrderQueryBean(refundRequestNo,flightInfo.getAirCode());
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(refundRequestNo, RequestTicketMethodEnums.QUERY_REFUND_OREDER.name(), getChannel(), bean, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            List<NameValuePair> requestParm = buildParam(bean);
            logger.info("请求自动出票退票订单查询接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出票退票订单查询耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票退票订单查询接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票退票订单查询接口未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
            logger.info("请求航司支付成功");
            TicketRefundResponseBean responseBean = JSONUtils.jsonToBean(responseData, TicketRefundResponseBean.class);
            return responseBean;

        } catch (Exception e) {
            logger.error("请求自动出票查退票订单查询出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;
    }

    public RealPriceRequestBean buildRequestBean(QueryFlightInfoBean info) {
        RealPriceRequestBean bean = new RealPriceRequestBean();
        bean.setCmd("NewRealPrice");
        bean.setCustomerSign(getMerchantCustomerSign());
        bean.setRequestId(info.getRequestChannelNo());
        bean.setAir2Code(info.getAirCode());
        bean.setAirChannel("www");
        bean.setDeptAirport(info.getDeptAirport());
        bean.setArrAirport(info.getArrAirport());
        bean.setDeptDate(info.getDeptDate());
        bean.setAdultNum(info.getAdultNum());
        bean.setChildNum(info.getChildNum());
        //构建hmac
        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("requestId");
        hmacList.add("air2Code");
        hmacList.add("airChannel");
        hmacList.add("deptAirport");
        hmacList.add("arrAirport");
        hmacList.add("deptDate");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        String key = getMerchantKey();
        logger.info("请求自动出票获取政策生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票获取政策生成hmac为{}", hmac);
        //构建token
        String tokenValue = bean.getRequestId() + perfix + airAccountPwd + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票获取政策token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票获取征程token的值为{}", token);
        bean.setHmac(hmac);
        bean.setToken(token);
        return bean;
    }





    public TicketRefundRequestBean buildRefundBean(String requestId, String refundRequestNo, List<PassengerEntity> passengers, String refundReason, ImmutablePair<String,String> file,String airCode) {
        TicketRefundRequestBean bean = new TicketRefundRequestBean();
        String key = getMerchantKey();
        String merchantNo = getMerchantCustomerSign();

        bean.setCustomerSign(merchantNo);
        bean.setRequestId(requestId);
        bean.setAir2Code(airCode);
        bean.setRefundRequestNo(refundRequestNo);
        String tokenValue = refundRequestNo + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票退票token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票退票token的值为{}", token);
        bean.setToken(token);
        List<TicketPassagerBean> list = new ArrayList<>();
        for (PassengerEntity passenger : passengers) {
            TicketPassagerBean passagerBean = new TicketPassagerBean();
            passagerBean.setName(passenger.getName());
            passagerBean.setType(passenger.getPersonType());
            passagerBean.setCretNum(passenger.getCertNum());
            passagerBean.setCertType(passenger.getCertType());
            list.add(passagerBean);
        }

        bean.setPassenger(JSONUtils.toJsonString(list));
        bean.setRefundReason(refundReason);
        if(!CheckUtils.isEmpty(file)){
            Map<String,String> map = new HashMap<>();
            map.put("fileName",file.getLeft());
            map.put("fileContent",file.getRight());
            List<Map> files = new ArrayList<>();
            files.add(map);
            bean.setUploadFileInfo(JSONUtils.toJsonString(files));
        }


        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("requestId");
        hmacList.add("refundRequestNo");
        hmacList.add("air2Code");
        hmacList.add("token");
        hmacList.add("airChannel");
        hmacList.add("passenger");

        String hmacValue = buildEncryptValue(hmacList, bean, null);
        logger.info("请求自动出票支付出票生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票支付出票生成hmac为{}", hmac);

        bean.setHmac(hmac);

        return bean;
    }


    public TicketRefundOrderQueryRequestBean buildRefundOrderQueryBean(String refundRequestNo,String airCode) {
        TicketRefundOrderQueryRequestBean bean = new TicketRefundOrderQueryRequestBean();
        String key = getMerchantKey();
        String merchantNo = getMerchantCustomerSign();

        bean.setCustomerSign(merchantNo);
        bean.setRefundRequestNo(refundRequestNo);
        bean.setAir2Code(airCode);
        String tokenValue = refundRequestNo + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票退票订单查询接口的token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票退票订单查询接口的token的值为{}", token);
        bean.setToken(token);
        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("air2Code");
        hmacList.add("refundRequestNo");
        hmacList.add("token");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        logger.info("请求自动出票支付退票订单查询接口生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票支付退票订单查询接口生成hmac为{}", hmac);
        bean.setHmac(hmac);
        return bean;
    }

    private Map<String,String> getRequestB2bInfo(){
        Map<String,String> info = new HashMap<>();
        logger.info("当前处理逻辑所在环境是否为内测{}", IpServerStatisticUtils.getEnvironmentTest());
        if(IpServerStatisticUtils.getEnvironmentTest()){
            info = ConfigUtils.requestB2bMerchantInfoForNc(getChannel());
        }else{
            info = ConfigUtils.requestB2bMerchantInfo(getChannel());
        }
        return info;
    }

    private String getMerchantCustomerSign(){
        return getRequestB2bInfo().get("customerSign");
    }
    private String getMerchantKey(){
        return getRequestB2bInfo().get("key");
    }

    private String getMerchantLoginName(){
        return getRequestB2bInfo().get("loginName");
    }

    private String getMerchantPwd(){
        return getRequestB2bInfo().get("pwd");
    }

    private String getMerchantCommonUrl(){
        return getRequestB2bInfo().get("COMMON_URL");
    }


    public static void main(String[] args) {
        QueryFlightInfoBean bean = new QueryFlightInfoBean();
        bean.setDeptAirport("1");
        bean.setArrAirport("2");
        bean.setDeptDate("3");
        bean.setAdultNum("4");
        bean.setChildNum("5");
        TicketInterFaceService service = new TicketInterFaceService();
        RealPriceRequestBean bean1 = service.buildRequestBean(bean);
        System.out.println(bean1);


    }
}

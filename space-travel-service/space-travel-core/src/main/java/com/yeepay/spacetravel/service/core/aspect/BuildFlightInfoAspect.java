package com.yeepay.spacetravel.service.core.aspect;

import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.common.util.page.Pagenation;
import com.yeepay.spacetravel.service.core.annotation.BuildFlightInfoAnnotation;
import com.yeepay.spacetravel.service.core.annotation.UnifiedProcessReturnValueAnnotation;
import com.yeepay.spacetravel.service.facade.dto.base.BaseResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightBaseInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.enums.flight.Flight2CodeEnum;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import org.apache.commons.collections.MapUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: qingwang.gu
 * @Date: 2023/08/03 11:09
 * @Description:
 */
@Component
@Aspect
public class BuildFlightInfoAspect {
    private Logger logger = LoggerFactory.getLogger(BuildFlightInfoAspect.class);

    @AfterReturning(pointcut = "@annotation(buildFlightInfoAnnotation)", returning = "o")
    public void after(JoinPoint pjp, BuildFlightInfoAnnotation buildFlightInfoAnnotation, Object o) throws Throwable {
        try {
            if (o instanceof BaseResult) {
                BaseResult baseResult = (BaseResult) o;
                if (baseResult.getReturnCode().equals(ReturnCode.SUCCESS)) {
                    Object data = baseResult.getData();
                    if (data instanceof List) {
                        //返回的list
                        List<Object> dataList = (List<Object>) data;
                        for (Object o1 : dataList) {
                            if (o1 instanceof BaseResponseDTO) {
                                buildFlight(o1);
                            }
                        }

                    }else if(data instanceof BaseResponseDTO){
                        //返回的是 BaseResponseDTO子类对象
                        buildFlight(data);
                    }else if(data instanceof Pagenation){
                        //返回的是分页查询
                        Pagenation<Object> dataList = (Pagenation<Object>) data;
                        List<Object> result = dataList.getRows();
                        for (Object o1 : result) {
                            if(o1 instanceof BaseResponseDTO){
                                buildFlight(o1);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("在给返回值做aop的时候出现位置异常", e);
        }
    }

    public String getAir2Code(String air2Code) {
        air2Code = air2Code.replace("B2CYP", "");
        air2Code = air2Code.replace("_NDC2C", "");
        air2Code = air2Code.replace("_NDC", "");
        air2Code = air2Code.replace("NDC", "");
        air2Code = air2Code.replace("AIR_", "");
        air2Code = air2Code.replace("_TRAVELNDC", "");
        return air2Code;
    }

    public void buildFlight(Object o1){
        BaseResponseDTO baseResponseDTO = (BaseResponseDTO) o1;

        FlightBaseInfoResponseDTO flightBaseInfoResponseDTO = baseResponseDTO.getFlightBaseInfoResponseDTO();
        String air2Code = getAir2Code(flightBaseInfoResponseDTO.getAirCode());
        baseResponseDTO.getFlightBaseInfoResponseDTO().setAirName(Flight2CodeEnum.getAirName(air2Code));
        baseResponseDTO.getFlightBaseInfoResponseDTO().setAirIconUrl("https://img.yeepay.com/spaceTravel/img/icon/"+air2Code+".png");

        String operateFlightNo = flightBaseInfoResponseDTO.getOperateFlightNo();
        if (StringUtils.isNotBlank(operateFlightNo)) {

            String operateAir2Code = operateFlightNo.substring(0,2);
            baseResponseDTO.getFlightBaseInfoResponseDTO().setOperateAirName(Flight2CodeEnum.getAirName(operateAir2Code));
            baseResponseDTO.getFlightBaseInfoResponseDTO().setOperateIconUrl("https://img.yeepay.com/spaceTravel/img/icon/"+operateAir2Code+".png");
        }

    }
}

package com.yeepay.spacetravel.service.core.rule;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderPassengerRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.enums.passager.PassengerTypeEnums;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/10 18:47
 * @Description:
 */
public class MuNDCRule {
    private static Logger logger = LoggerFactory.getLogger(MuNDCRule.class);
    public static ImmutablePair<Boolean,String> checkPassager(List<TicketOrderPassengerRequestDTO> passagers){
        if(passagers.size()>9){
            logger.info("一个订单最多只能有9名乘机人");
            return new ImmutablePair<>(false,"一个订单最多只能有9名乘机人");
        }
        return new ImmutablePair<>(true,"成功");
    }

    public static boolean checkAmount(TicketOrderRequestDTO dto){
        // 往返程情况下，使用总价进行验证，暂时跳过详细的价格校验
        if (Boolean.TRUE.equals(dto.getIsRoundTrip())) {
            // 往返程：简单验证总价是否大于0
            if (dto.getTotalPrice() != null && dto.getTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
            // 如果没有总价，检查票面总价
            if (dto.getTicketTotalAmount() != null && dto.getTicketTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
            return false;
        }

        // 单程：使用原有逻辑
        if (dto.getFlight() == null) {
            return false;
        }

        //乘机人分组
        List<TicketOrderPassengerRequestDTO> adults = new ArrayList<>();
        List<TicketOrderPassengerRequestDTO> childs = new ArrayList<>();
        List<TicketOrderPassengerRequestDTO> babies = new ArrayList<>();

        for (TicketOrderPassengerRequestDTO passager : dto.getPassagers()) {
            if(passager.getType().equals(PassengerTypeEnums.ADULT.name())){
                adults.add(passager);
            }
            if(passager.getType().equals(PassengerTypeEnums.CHILD.name())){
                childs.add(passager);
            }
            if(passager.getType().equals(PassengerTypeEnums.BABY.name())){
                babies.add(passager);
            }
        }
        BigDecimal couponAmount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(dto.getFlight().getCouponAmount())){
            couponAmount = couponAmount.add(new BigDecimal(dto.getFlight().getCouponAmount()));
        }
        BigDecimal adultAmount = (new BigDecimal(dto.getFlight().getPriceAdult())
                .add(new BigDecimal(dto.getFlight().getTaxFeeAdult())).subtract(couponAmount))
                .multiply(new BigDecimal(adults.size()));
        if(dto.getTicketTotalAmount().compareTo(adultAmount)<0){
            return false;
        }else{
            return true;
        }
    }
}

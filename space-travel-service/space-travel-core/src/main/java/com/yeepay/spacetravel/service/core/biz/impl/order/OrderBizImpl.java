package com.yeepay.spacetravel.service.core.biz.impl.order;

import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.IdCard.AnalysisIdCardUtils;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.constants.redis.RedisConstants;
import com.yeepay.spacetravel.common.util.date.DateUtils;
import com.yeepay.spacetravel.common.util.exception.OperationException;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.common.util.redis.RedisIdUtils;
import com.yeepay.spacetravel.common.util.redis.RedisLockUtils;
import com.yeepay.spacetravel.common.util.validator.BeanValidator;
import com.yeepay.spacetravel.service.core.biz.order.OrderBiz;
import com.yeepay.spacetravel.service.core.biz.pay.PayBiz;
import com.yeepay.spacetravel.service.core.chanel.TicketServiceRouteFactory;
import com.yeepay.spacetravel.service.core.chanel.service.bean.B2BOrderInfo;
import com.yeepay.spacetravel.service.core.chanel.service.bean.B2BOrderPayBean;
import com.yeepay.spacetravel.service.core.chanel.service.bean.ConfigInfo;
import com.yeepay.spacetravel.service.core.chanel.service.service.AirLineService;
import com.yeepay.spacetravel.service.core.chanel.service.service.AirLineServiceFactory;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.TicketInterFaceBiz;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.TicketInterfaceBizFactory;
import com.yeepay.spacetravel.service.core.entity.MemberEntity;
import com.yeepay.spacetravel.service.core.entity.b2b.B2bUserEntity;
import com.yeepay.spacetravel.service.core.entity.bank.RequestBankEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderSubEntity;
import com.yeepay.spacetravel.service.core.entity.passenger.PassengerEntity;
import com.yeepay.spacetravel.service.core.entity.ticket.RequestTicketEntity;
import com.yeepay.spacetravel.service.core.rule.MuNDCRule;
import com.yeepay.spacetravel.service.core.service.MemberService;
import com.yeepay.spacetravel.service.core.service.b2b.B2bUserService;
import com.yeepay.spacetravel.service.core.service.bank.RequestBankService;
import com.yeepay.spacetravel.service.core.service.cityAirport.CityAirportService;
import com.yeepay.spacetravel.service.core.service.flight.FlightInfoService;
import com.yeepay.spacetravel.service.core.service.order.OrderService;
import com.yeepay.spacetravel.service.core.service.order.OrderSubService;
import com.yeepay.spacetravel.service.core.service.passenger.PassengerService;
import com.yeepay.spacetravel.service.core.service.ticket.RequestTicketService;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BContactDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BCreateOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BOrderPayRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BPassengerDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.OrderPagePaySuccessRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderFlightRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderPassengerRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.response.B2BCreateOrderResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.order.response.B2BOrderPayResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.order.response.OrderPagePaySuccessResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.order.response.TicketOrderResponseDTO;
import com.yeepay.spacetravel.service.facade.enums.bank.BankRequestStatusEnus;
import com.yeepay.spacetravel.service.facade.enums.bank.BankRequestTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.order.OrderStatusEnums;
import com.yeepay.spacetravel.service.facade.enums.order.OrderTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.passager.CertType;
import com.yeepay.spacetravel.service.facade.enums.passager.PassengerTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.passager.PassengerUseTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketMethodEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketStatusEnums;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.facade.result.b2b.B2bBaseResult;
import com.yeepay.utils.lock.impl.RedisLock;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/10 16:20
 * @Description:
 */
@Service
public class OrderBizImpl implements OrderBiz {
    private Logger logger = LoggerFactory.getLogger(OrderBizImpl.class);
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderSubService orderSubService;
    @Autowired
    private CityAirportService cityAirportService;
    @Autowired
    private TicketServiceRouteFactory ticketServiceRouteFactory;
    @Autowired
    private RequestTicketService requestTicketService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private PayBiz payBiz;
    @Autowired
    private PassengerService passengerService;
    @Autowired
    private RequestBankService requestBankService;

    @Autowired
    private FlightInfoService flightInfoService;

    @Autowired
    private B2bUserService b2bUserService;

    private static final String ORDER_SUCCESS = "ORDER_SUCCESS";

    @Override
    public BaseResult<TicketOrderResponseDTO> order(TicketOrderRequestDTO dto) {
        logger.info("请求下单的参数为{}", JSONUtils.toJsonString(dto));
        BeanValidator.validate(dto);
        // 检查航司二字码：优先检查去程，其次单程，最后回程
        String airCode = null;
        if (dto.getDeparture() != null && StringUtils.isNotBlank(dto.getDeparture().getAirCode())) {
            airCode = dto.getDeparture().getAirCode();
        } else if (dto.getFlight() != null && StringUtils.isNotBlank(dto.getFlight().getAirCode())) {
            airCode = dto.getFlight().getAirCode();
        } else if (dto.getReturn_() != null && StringUtils.isNotBlank(dto.getReturn_().getAirCode())) {
            airCode = dto.getReturn_().getAirCode();
        }

        if(CheckUtils.isEmpty(airCode)){
            return BaseResult.fail(ReturnCode.PARAM_ERROR,"航司二字码必填");
        }
        //判断memberid是否存在
       // MemberEntity member = memberService.checkMember(dto.getMemberId());
        //补全乘机人信息 成人、生日
        dto = completionPassgerInfo(dto);
        //参数校验，1、验证携带儿童成人等个数问题 2、验证金额
        MuNDCRule.checkPassager(dto.getPassagers());
        if(!MuNDCRule.checkAmount(dto)){
            return BaseResult.fail(ReturnCode.ORDER_FAIL,"机票总价异常");
        }
        RedisLock redisLock = redisLock(dto);
        try {
            //验证是否重复下单，判断是否存在相同待支付订单
            checkWaitPayOrder(dto);
            //创建订单
            OrderEntity order = buildOrderInfo(dto);
            //创建requestticket
            RequestTicketEntity requestTicketEntity = new RequestTicketEntity(ticketServiceRouteFactory.tikcetServiceRouter(),RequestTicketMethodEnums.ORDER.name(), order.getId(),RequestTicketMethodEnums.ORDER.name(),order.getOrderAmount());
            requestTicketService.add(requestTicketEntity);
            //请求出票机构进行下单
            TicketInterFaceBiz ticketBiz = TicketInterfaceBizFactory.getService(requestTicketEntity.getRequestChannel());
            TicketOrderResponseBean responseBean = ticketBiz.order(dto,order.getId(),requestTicketEntity.getRequestChannelOrderNo());
            //更新订单
            ImmutablePair<OrderEntity,RequestTicketEntity> pair = updateOrder(responseBean,order,requestTicketEntity);
            order = pair.getLeft();
            if(OrderStatusEnums.ORDER_SUCCESS.name().equals(order.getStatus())){
                TicketOrderResponseDTO responseDTO = new TicketOrderResponseDTO();
                responseDTO.setOrderId(order.getId());
                responseDTO.setPayPrice(order.getOrderAmount());
                return BaseResult.success(responseDTO);
            }else{
                String message = ReturnCode.getRetMsg(ReturnCode.ORDER_FAIL);
                if(!CheckUtils.isEmpty(pair.getRight().getChannelRetMsg())){
                    message = pair.getRight().getChannelRetMsg();
                }
               return BaseResult.fail(ReturnCode.ORDER_FAIL,message);
            }
        }catch (OperationException e){
            logger.info("小程序下单过程中出现系统内部异常",e);
            return BaseResult.fail(e.getDefineCode(),e.getMessage());
        } catch (Exception e) {
            logger.info("进行小程序下单过程中出现未知异常",e);
            return BaseResult.fail(ReturnCode.FAIL,e.getMessage());
        } finally {
            if (null != redisLock) {
                redisLock.unlock();
            }
        }

    }

    @Override
    public BaseResult<OrderPagePaySuccessResponseDTO> paySuccess(OrderPagePaySuccessRequestDTO dto) {
        logger.info("前端页面支付成功请求参数为{}",JSONUtils.toJsonString(dto));
        BeanValidator.validate(dto);
        //判断memberid是否存在
        MemberEntity member = memberService.checkMember(dto.getMemberId());
        //判断订单是否属于同一个人
        OrderEntity order = orderService.get(dto.getOrderId());
        if(CheckUtils.isEmpty(order)){
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,ReturnCode.getRetMsg(ReturnCode.PARAM_ERROR));
        }
        if(!dto.getMemberId().equals(order.getMemberId())){
            logger.info("该订单不属于该用户{},{}",order.getMemberId(),dto.getMemberId());
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"该订单不属于该用户");
        }
        //如果订单状态已经是出票中了直接返回
        if (!OrderStatusEnums.ORDER_SUCCESS.name().equals(order.getStatus()) && !OrderStatusEnums.WAIT_PAY.name().equals(order.getStatus()) && !OrderStatusEnums.INIT.name().equals(order.getStatus())) {
            OrderPagePaySuccessResponseDTO responseDTO = new OrderPagePaySuccessResponseDTO();
            responseDTO.setOrderId(order.getId());
            responseDTO.setStatus(order.getStatus());
            return BaseResult.success(responseDTO);
        }
        RedisLock redisLock = null;
       try{
           redisLock = RedisLockUtils.lock(RedisConstants.ORDER_STATUS_REDIS_KEY + order.getId(), 60);
           //判断支付订单是否存在支付中的
           List<RequestBankEntity> dealingList = requestBankService.queryDealingEntity(order.getId(),new String[]{BankRequestStatusEnus.PROCESS.name()}, BankRequestTypeEnums.PAY.name());
           if(CollectionUtils.isEmpty(dealingList)){
               logger.info("不存在调用易宝聚合下单成功的requestbank,不能作页面支付成功");
               throw OperationException.newInstantce(ReturnCode.ORDER_STATUS_ERROR,ReturnCode.getRetMsg(ReturnCode.ORDER_STATUS_ERROR));
           }
           //更新订单状态为支付成功
           order.setStatus(OrderStatusEnums.PAYING.name());
           order.setUpdateTime(new Date());
           orderService.update(order);
           logger.info("将订单更新为支付中");
           OrderPagePaySuccessResponseDTO responseDTO = new OrderPagePaySuccessResponseDTO();
           responseDTO.setOrderId(order.getId());
           responseDTO.setStatus(order.getStatus());
           return BaseResult.success(responseDTO);
       }catch (Exception e){
            logger.info("前端支付成功处理异常",e);
            return BaseResult.fail(ReturnCode.FAIL,ReturnCode.getRetMsg(ReturnCode.FAIL));
       }finally {
           if(null != redisLock){
               redisLock.unlock();
           }
       }
    }

    @Override
    public B2bBaseResult<B2BCreateOrderResponseDTO> createOrder(B2BCreateOrderRequestDTO dto) {

        logger.info("B2B请求下单的参数为{}", JSONUtils.toJsonString(dto));
        BeanValidator.validate(dto);

        RedisLock redisLock = b2bRedisLock(dto);
        try {
            //验证是否重复下单，判断是否存在相同待支付订单
            b2bCheckWaitPayOrder(dto);
            //创建订单
            OrderEntity order = b2bBuildOrderInfo(dto);
            B2BCreateOrderResponseDTO responseDTO = new B2BCreateOrderResponseDTO();
            responseDTO.setOrderId(order.getId());
            return B2bBaseResult.success(responseDTO);
        } catch (OperationException e) {
            logger.info("B2B请求下单过程中出现系统内部异常", e);
            return B2bBaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.info("B2B请求下单过程中出现未知异常", e);
            return B2bBaseResult.fail(ReturnCode.FAIL, e.getMessage());
        } finally {
            if (null != redisLock) {
                redisLock.unlock();
            }
        }
    }

    @Override
    public B2bBaseResult<B2BOrderPayResponseDTO> orderPay(B2BOrderPayRequestDTO dto) {
        logger.info("B2B订单支付的参数为：{}", JSONUtils.toJsonString(dto));
        BeanValidator.validate(dto);
        RedisLock redisLock = b2bOrderPayRedisLock(dto);
        try {
            B2BOrderPayBean b2BOrderPayBean = buildB2BOrderPayBean(dto);

            checkOrderStatus(b2BOrderPayBean);
            AirLineService service = AirLineServiceFactory.getService(b2BOrderPayBean.getAirCode());
            if (service == null) {
                // 按道理是不会触发这个的
                throw OperationException.newInstantce("000001", "不支持当前航司");
            }
            RequestTicketEntity requestTicketEntity = new RequestTicketEntity("YeePay",RequestTicketMethodEnums.PAY_TICKET.name(), b2BOrderPayBean.getOrderEntity().getId(),RequestTicketMethodEnums.PAY_TICKET.name(),new BigDecimal("0"),b2BOrderPayBean.getOrderEntity().getId());
            requestTicketService.add(requestTicketEntity);

            B2BOrderInfo b2BOrderInfo = service.orderPay(b2BOrderPayBean);

            B2BOrderPayResponseDTO responseDTO = buildB2BOrderPayResponseDTO(b2BOrderInfo);
            return B2bBaseResult.success(responseDTO);
        } catch (OperationException e) {
            logger.info("B2B订单支付过程中出现系统内部异常", e);
            return B2bBaseResult.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.info("进行B2B订单支付过程中出现未知异常", e);
            return B2bBaseResult.fail(ReturnCode.FAIL, e.getMessage());
        } finally {
            if (null != redisLock) {
                redisLock.unlock();
            }
        }
    }

    private void updateRequestTicket(RequestTicketEntity requestTicketEntity, B2BOrderInfo b2BOrderInfo) {
        if (OrderStatusEnums.TICKET_FAIL.name().equals(b2BOrderInfo.getStatus())) {
            requestTicketEntity.setStatus(RequestTicketStatusEnums.FAIL.name());
        }
        if (OrderStatusEnums.TICKET_SUCCESS.name().equals(b2BOrderInfo.getStatus())) {
            requestTicketEntity.setStatus(RequestTicketStatusEnums.SUCCESS.name());
        }
        requestTicketEntity.setUpdateTime(new Date());
        requestTicketService.update(requestTicketEntity);
    }

    private void checkOrderStatus(B2BOrderPayBean b2BOrderPayBean) {
        if (!OrderStatusEnums.WAIT_PAY.name().equals(b2BOrderPayBean.getOrderEntity().getStatus())) {
            throw OperationException.newInstantce("000001", "当前订单状态错误，不支持发起支付");
        }
    }

    @NotNull
    private B2BOrderPayResponseDTO buildB2BOrderPayResponseDTO(B2BOrderInfo b2BOrderInfo) {

        String status = b2BOrderInfo.getStatus();
        if (OrderStatusEnums.TICKET_FAIL.name().equals(status)) {
            throw OperationException.newInstantce("000002", "支付出票失败，原因：" + b2BOrderInfo.getMessage());
        }
        B2BOrderPayResponseDTO responseDTO = new B2BOrderPayResponseDTO();
        responseDTO.setOrderId(b2BOrderInfo.getOrderId());
        responseDTO.setStatus(status);
        return responseDTO;
    }

    @NotNull
    private B2BOrderPayBean buildB2BOrderPayBean(B2BOrderPayRequestDTO dto) {
        // 查询数据库信息
        String orderId = dto.getOrderId();
        OrderEntity orderEntity = orderService.get(orderId);
        List<OrderSubEntity> subEntityList = orderSubService.queryOrderSubsByOrderId(orderId);
        FlightInfoEntity flightInfoEntity = flightInfoService.queryByOrderId(orderId);
        List<PassengerEntity> passengerEntityList = passengerService.queryByOrderId(orderId);

        B2BOrderPayBean b2BOrderPayBean = new B2BOrderPayBean();
        b2BOrderPayBean.setOrderId(dto.getOrderId());
        b2BOrderPayBean.setUserName(dto.getUserName());
        b2BOrderPayBean.setPwd(dto.getPwd());
        b2BOrderPayBean.setOrderEntity(orderEntity);
        b2BOrderPayBean.setSubEntityList(subEntityList);
        b2BOrderPayBean.setFlightInfoEntity(flightInfoEntity);
        b2BOrderPayBean.setPassengerEntityList(passengerEntityList);
        B2bUserEntity b2bUserEntity = b2bUserService.get(dto.getId());
        b2BOrderPayBean.setB2bUserEntity(b2bUserEntity);
        b2BOrderPayBean.setConfigInfo(JSONUtils.jsonToBean(b2bUserEntity.getInfo(), ConfigInfo.class));
        b2BOrderPayBean.setRequestChannelNo(dto.getOrderId());
        b2BOrderPayBean.setAirCode(flightInfoEntity.getAirCode());
        return b2BOrderPayBean;
    }


    public TicketOrderRequestDTO completionPassgerInfo(TicketOrderRequestDTO dto) {
        try {
            List<TicketOrderPassengerRequestDTO> list = new ArrayList<>();
            for (TicketOrderPassengerRequestDTO passager : dto.getPassagers()) {
                if(CertType.NI.name().equals(passager.getCertType())){
                    String birthday = AnalysisIdCardUtils.analysisBirthday(passager.getCretNum());
                    logger.info("乘机人姓名{}生日{}", passager.getName(), birthday);
                    passager.setBirthday(birthday);

                    // 获取起飞时间：优先使用去程，其次单程，最后回程
                    String deptTime = null;
                    if (dto.getDeparture() != null && dto.getDeparture().getDeptTime() != null) {
                        deptTime = dto.getDeparture().getDeptTime();
                    } else if (dto.getFlight() != null && dto.getFlight().getDeptTime() != null) {
                        deptTime = dto.getFlight().getDeptTime();
                    } else if (dto.getReturn_() != null && dto.getReturn_().getDeptTime() != null) {
                        deptTime = dto.getReturn_().getDeptTime();
                    }

                    if (deptTime != null) {
                        Date depTime = DateUtils.sdfDateOnly.parse(deptTime.substring(0, 10));
                        passager.setType(AnalysisIdCardUtils.getPersonType(passager.getCretNum(), depTime));
                        logger.info("乘机人姓名{}类型{}", passager.getName(), passager.getCertType());
                        passager.setAge(AnalysisIdCardUtils.getUserAge(birthday, depTime));
                        logger.info("乘机人姓名{}年龄{}", passager.getName(), passager.getAge());
                    }
                    list.add(passager);
                }else{
                    list.add(passager);
                }
            }
            dto.setPassagers(list);
        } catch (Exception e) {
            logger.error("补全乘机人信息异常");
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "乘机人信息异常");
        }
        return dto;
    }

    public RedisLock redisLock(TicketOrderRequestDTO dto) {
        String certNum = "";
        for (TicketOrderPassengerRequestDTO passager : dto.getPassagers()) {
            certNum = certNum + passager.getCretNum() + "_";
        }

        // 获取航班号：优先使用去程，其次单程，最后回程
        String flightNo = "";
        if (dto.getDeparture() != null && dto.getDeparture().getFlightNo() != null) {
            flightNo = dto.getDeparture().getFlightNo();
        } else if (dto.getFlight() != null && dto.getFlight().getFlightNo() != null) {
            flightNo = dto.getFlight().getFlightNo();
        } else if (dto.getReturn_() != null && dto.getReturn_().getFlightNo() != null) {
            flightNo = dto.getReturn_().getFlightNo();
        }

        String key = dto.getMemberId() + "_" + flightNo + "_" + certNum;
        RedisLock redisLock = null;
        try{
             redisLock = RedisLockUtils.lock(key, 20);
        }catch (Exception e){
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "您又有一笔相同订单正在处理,请稍后在提交");
        }


        return redisLock;
    }


    public RedisLock b2bOrderPayRedisLock(B2BOrderPayRequestDTO dto) {

        String key = dto.getOrderId();
        RedisLock redisLock = null;
        try {
            redisLock = RedisLockUtils.lock(key, 20);
        } catch (Exception e) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "您又有一笔相同订单正在处理,请稍后在提交");
        }
        return redisLock;
    }

    public RedisLock b2bRedisLock(B2BCreateOrderRequestDTO dto) {
        String certNum = "";
        for (B2BPassengerDTO passengerDTO : dto.getB2BPassengerParamList()) {
            certNum = certNum + passengerDTO.getCertNo() + "_";
        }
        String key = dto.getId() + "_" + dto.getAirCode() + "_" + dto.getFlightNo() + "_" + dto.getDeptTime() + "_" + certNum;
        RedisLock redisLock = null;
        try {
            redisLock = RedisLockUtils.lock(key, 20);
        } catch (Exception e) {
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "您又有一笔相同订单正在处理,请稍后在提交");
        }
        return redisLock;
    }


    public boolean b2bCheckWaitPayOrder(B2BCreateOrderRequestDTO dto) {
        String[] orderStatuStrings = new String[]{OrderStatusEnums.INIT.name(), OrderStatusEnums.ORDER_SUCCESS.name(), OrderStatusEnums.WAIT_PAY.name()};
        List<String> orderStatus = Arrays.asList(orderStatuStrings);
        List<String> certNums = new ArrayList<>();
        for (B2BPassengerDTO passager : dto.getB2BPassengerParamList()) {
            certNums.add(passager.getCertNo());
        }
        OrderEntity orderEntity = orderService.queryWaitPayOrder(dto.getId(), orderStatus, dto.getFlightNo(), dto.getDeptTime(), certNums);

        if (!CheckUtils.isEmpty(orderEntity)) {
            List<OrderSubEntity> orderSubs = orderSubService.queryOrderSubsByOrderId(orderEntity.getId());
            if (dto.getB2BPassengerParamList().size() == orderSubs.size()) {
                boolean flag = true;
                for (OrderSubEntity orderSub : orderSubs) {
                    PassengerEntity passenger = passengerService.get(orderSub.getPassengerId());
                    if (!certNums.contains(passenger.getCertNum())) {
                        flag = false;
                    }
                }
                if (flag) {
                    throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "您为乘机人购买的航班已经存在订单,请求到待支付订单列表进行支付");
                }

            }
        }
        return true;
    }



    public boolean checkWaitPayOrder(TicketOrderRequestDTO dto) {
        String[] orderStatuStrings = new String[]{OrderStatusEnums.INIT.name(), OrderStatusEnums.ORDER_SUCCESS.name(), OrderStatusEnums.WAIT_PAY.name()};
        List<String> orderStatus = Arrays.asList(orderStatuStrings);
        List<String> certNums = new ArrayList<>();
        for (TicketOrderPassengerRequestDTO passager : dto.getPassagers()) {
            certNums.add(passager.getCretNum());
        }
        String deptTime = null;
        String flightNo = null;
        if ("RT".equals(dto.getJourneyType())) {
            deptTime = dto.getDeparture().getDeptTime();
            flightNo = dto.getDeparture().getFlightNo();
        }else {
            deptTime = dto.getFlight().getDeptTime();
            flightNo = dto.getFlight().getFlightNo();
        }
        OrderEntity orderEntity = orderService.queryWaitPayOrder(dto.getMemberId(), orderStatus, flightNo,deptTime, certNums);

        if (!CheckUtils.isEmpty(orderEntity)) {
            List<OrderSubEntity> orderSubs = orderSubService.queryOrderSubsByOrderId(orderEntity.getId());
            if (dto.getPassagers().size() == orderSubs.size()) {
                boolean flag = true;
                for (OrderSubEntity orderSub : orderSubs) {
                    PassengerEntity passenger = passengerService.get(orderSub.getPassengerId());
                    if(!certNums.contains(passenger.getCertNum())){
                        flag = false;
                    }
                }
                if(flag){
                    throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "您为乘机人购买的航班已经存在订单,请求到待支付订单列表进行支付");
                }

            }
        }
        return true;
    }

    public OrderEntity b2bBuildOrderInfo(B2BCreateOrderRequestDTO dto) {
        logger.info("开始创建系统订单相关的数据");
        String contactId = RedisIdUtils.getId();
        //创建order相关
        OrderEntity order = new OrderEntity();
        order.setId(RedisIdUtils.getId());
        order.setVersion(0L);
        order.setOrderNo(RedisIdUtils.getId());
        order.setMemberId(dto.getId());
        order.setOrderType(OrderTypeEnums.PLANE_TICKET.name());
        order.setOrderAmount(new BigDecimal(dto.getPayPrice()));
        order.setPayAmount(new BigDecimal(dto.getPayPrice()));
        order.setCurrencyCode(dto.getCurrencyCode());
        order.setPayCurrencyCode(dto.getCurrencyCode());
        order.setBuyCount(dto.getB2BPassengerParamList().size());
        order.setOrderTime(new Date());
        order.setOrderComplete(new Date());
        order.setStatus(OrderStatusEnums.WAIT_PAY.name());
        order.setContactsId(contactId);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        List<PassengerEntity> passagers = new ArrayList<>();
        List<OrderSubEntity> orderSubs = new ArrayList<>();
        for (B2BPassengerDTO passager : dto.getB2BPassengerParamList()) {
            //创建passager相关
            PassengerEntity passagerEntity = new PassengerEntity();
            passagerEntity.setId(RedisIdUtils.getId());
            passagerEntity.setVersion(0L);
            passagerEntity.setMemberId(dto.getId());
            passagerEntity.setName(passager.getFirstName()+passager.getLastName());
            passagerEntity.setFirstName(passager.getFirstName());
            passagerEntity.setLastName(passager.getLastName());
            passagerEntity.setCountry(passager.getCountry());
            passagerEntity.setSex(passager.getSex());
            passagerEntity.setIssuingCountryCode(passager.getIssuingCountryCode());
            passagerEntity.setExpiryDate(passager.getExpiryDate());
            passagerEntity.setCertType(passager.getCertType());
            passagerEntity.setCertNum(passager.getCertNo());
            passagerEntity.setPhone(passager.getTel());
            try {
                passagerEntity.setBirthday(DateUtils.sdfDateOnly.parse(passager.getBirthday()));
            } catch (ParseException e) {
                logger.info("生日转换异常", e);
            }
            String type = passager.getType();
            passagerEntity.setPersonType(type);
            passagerEntity.setUseType(PassengerUseTypeEnums.PASSAGER.name());
            passagerEntity.setCreateTime(new Date());
            passagerEntity.setUpdateTime(new Date());
            passagers.add(passagerEntity);
            //创建orderSub相关
            OrderSubEntity orderSubEntity = new OrderSubEntity();
            orderSubEntity.setId(RedisIdUtils.getId());
            orderSubEntity.setVersion(0L);
            orderSubEntity.setOrderSubNo(RedisIdUtils.getId());
            orderSubEntity.setOrderId(order.getId());
            orderSubEntity.setPassengerId(passagerEntity.getId());

            if ("ADULT".equals(type)) {
                orderSubEntity.setTicketAmount(new BigDecimal(dto.getTicketParAdult()));
                orderSubEntity.setTaxFee(new BigDecimal(dto.getTaxFeeAdult()));
            } else {
                orderSubEntity.setTicketAmount(new BigDecimal(dto.getTicketParChild()));
                orderSubEntity.setTaxFee(new BigDecimal(dto.getTaxFeeChild()));
            }

            orderSubEntity.setCreateTime(new Date());
            orderSubEntity.setUpdateTime(new Date());
            orderSubs.add(orderSubEntity);
        }
        //创建联系人
        PassengerEntity contact = new PassengerEntity();
        contact.setId(contactId);
        contact.setVersion(0L);
        contact.setMemberId(dto.getId());
        B2BContactDTO b2BContactParam = dto.getB2BContactParam();
        contact.setName(b2BContactParam.getContactName());
        contact.setPhone(b2BContactParam.getContactMobile());
        contact.setUseType(PassengerUseTypeEnums.CONTACT.name());
        contact.setCreateTime(new Date());
        contact.setUpdateTime(new Date());
        contact.setEmail(b2BContactParam.getContactEmail());
        contact.setCountry(b2BContactParam.getContactCountryCode());
        passagers.add(contact);

        OrderSubEntity orderSubEntity = new OrderSubEntity();
        orderSubEntity.setId(RedisIdUtils.getId());
        orderSubEntity.setVersion(0L);
        orderSubEntity.setOrderSubNo(RedisIdUtils.getId());
        orderSubEntity.setOrderId(order.getId());
        orderSubEntity.setPassengerId(contact.getId());

        orderSubEntity.setCreateTime(new Date());
        orderSubEntity.setUpdateTime(new Date());
        orderSubs.add(orderSubEntity);

        //创建航班信息
        FlightInfoEntity flight = new FlightInfoEntity();
        flight.setId(RedisIdUtils.getId());
        flight.setVersion(0L);
        flight.setFlightNo(dto.getFlightNo());
//        try {
        flight.setDepartureDate(parseDate(dto.getDeptTime().substring(0, 10),DateUtils.DATE_FORMAT_DATEONLY));
        flight.setDepartureTime(parseDate(dto.getDeptTime(),DateUtils.DATE_FORMAT_DATETIME_MINUTE));
        flight.setArrivalDate(parseDate(dto.getArrTime().substring(0, 10),DateUtils.DATE_FORMAT_DATEONLY));
        flight.setArrivalTime(parseDate(dto.getArrTime(),DateUtils.DATE_FORMAT_DATETIME_MINUTE));
//        } catch (ParseException e) {
//            logger.error("创建航班订单信息时时间转换错误");
//        }

        Map<String, String> airPortAndName = cityAirportService.queryAllAirPort();
        Map<String, String> airPortAndCityName = cityAirportService.queryAllAirPortInCityName();
        flight.setAirCode(dto.getAirCode());
        flight.setDeptAirport(dto.getDeptAirport());
        flight.setDepartureCityName(airPortAndCityName.get(dto.getDeptAirport()));
        flight.setDepartureAirportName(airPortAndName.get(dto.getDeptAirport()));

        flight.setArrAirport(dto.getArrAirport());
        flight.setArrivalCityName(airPortAndCityName.get(dto.getArrAirport()));
        flight.setArrivalAirportName(airPortAndName.get(dto.getArrAirport()));
        flight.setCabinClass(dto.getCabin());
        flight.setCabinType(dto.getCabinType());
        flight.setFlightModel(dto.getPlaneType());
        flight.setCreateTime(new Date());
        flight.setUpdateTime(new Date());
        flight.setOrderId(order.getId());
        String taxFeeChild = dto.getTaxFeeChild();
        if (StringUtils.isNotBlank(taxFeeChild)) {
            flight.setTaxChild(taxFeeChild);
        }
        String ticketParChild = dto.getTicketParChild();
        if (StringUtils.isNotBlank(ticketParChild)) {
            flight.setPriceChild(ticketParChild);
        }

        flight.setTaxAdult(dto.getTaxFeeAdult());
        flight.setPriceAdult(dto.getTicketParAdult());
        flight.setRuleService(dto.getRefundAndChangeRuleDes());

        orderService.saveOrderAndRelationTables(order, orderSubs, passagers, flight);
        logger.info("系统订单创建成功");
        return order;
    }

    private Date parseDate(String time,String text) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(text);
            return format.parse(time);
        } catch (Exception e) {
            logger.error("转化时间异常：",e);
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR, "日期转化异常");
        }
    }



    public OrderEntity buildOrderInfo(TicketOrderRequestDTO dto) {
        logger.info("开始创建系统订单相关的数据");
        String contactId = RedisIdUtils.getId();
        //创建order相关
        OrderEntity order = new OrderEntity();
        order.setId(RedisIdUtils.getId());
        order.setVersion(0L);
        order.setOrderNo(RedisIdUtils.getId());
        order.setMemberId(dto.getMemberId());
        order.setOrderType(OrderTypeEnums.PLANE_TICKET.name());
        order.setOrderAmount(dto.getTicketTotalAmount());
        order.setBuyCount(dto.getPassagers().size());
        order.setOrderTime(new Date());
        order.setOrderComplete(new Date());
        order.setStatus(OrderStatusEnums.INIT.name());
        order.setContactsId(contactId);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        List<PassengerEntity> passagers = new ArrayList<>();
        List<OrderSubEntity> orderSubs = new ArrayList<>();
        for (TicketOrderPassengerRequestDTO passager : dto.getPassagers()) {
            //创建passager相关
            PassengerEntity passagerEntity = new PassengerEntity();
            passagerEntity.setId(RedisIdUtils.getId());
            passagerEntity.setVersion(0L);
            passagerEntity.setMemberId(dto.getMemberId());
            passagerEntity.setName(passager.getName());
            passagerEntity.setCertType(passager.getCertType());
            passagerEntity.setCertNum(passager.getCretNum());
            passagerEntity.setPhone(passager.getTel());
            try {
                passagerEntity.setBirthday(DateUtils.sdfDateOnly.parse(passager.getBirthday()));
            } catch (ParseException e) {
                logger.info("生日转换异常", e);
            }

            String passengerType = passager.getType();
            if (StringUtils.isNotBlank(passager.getPassengerType())) {
                passengerType = passager.getPassengerType();
            }
            passagerEntity.setPersonType(passengerType);
            passagerEntity.setUseType(PassengerUseTypeEnums.PASSAGER.name());
            passagerEntity.setCreateTime(new Date());
            passagerEntity.setUpdateTime(new Date());
            passagers.add(passagerEntity);
            //创建orderSub相关
            OrderSubEntity orderSubEntity = new OrderSubEntity();
            orderSubEntity.setId(RedisIdUtils.getId());
            orderSubEntity.setVersion(0L);
            orderSubEntity.setOrderSubNo(RedisIdUtils.getId());
            orderSubEntity.setOrderId(order.getId());
            orderSubEntity.setPassengerId(passagerEntity.getId());
            BigDecimal ticketAmount = new BigDecimal("0");
            BigDecimal taxFee = BigDecimal.ZERO;
            BigDecimal fuelFee = BigDecimal.ZERO;

            if (Boolean.TRUE.equals(dto.getIsRoundTrip())) {
                // 往返程：使用总价除以乘客数量
                if (dto.getTotalPrice() != null) {
                    ticketAmount = dto.getTotalPrice().divide(new BigDecimal(dto.getPassagers().size()), 2, BigDecimal.ROUND_HALF_UP);
                }
                // 往返程的税费和燃油费可以从去程航班获取
                if (dto.getDeparture() != null) {
                    if (PassengerTypeEnums.ADULT.name().equals(passengerType)) {
                        if (StringUtils.isNotBlank(dto.getDeparture().getAmtAdultAirPortFee())) {
                            taxFee = new BigDecimal(dto.getDeparture().getAmtAdultAirPortFee()).multiply(new BigDecimal("2")); // 往返程双倍
                        }
                        if (StringUtils.isNotBlank(dto.getDeparture().getOilFeeAdult())) {
                            fuelFee = new BigDecimal(dto.getDeparture().getOilFeeAdult()).multiply(new BigDecimal("2")); // 往返程双倍
                        }
                    } else if (PassengerTypeEnums.CHILD.name().equals(passengerType)) {
                        String amtChildAirPortFee = dto.getDeparture().getAmtChildAirPortFee();
                        String oilFeeChild = dto.getDeparture().getOilFeeChild();
                        if (StringUtils.isNotBlank(amtChildAirPortFee)) {
                            taxFee = new BigDecimal(amtChildAirPortFee).multiply(new BigDecimal("2")); // 往返程双倍
                        }
                        if (StringUtils.isNotBlank(oilFeeChild)) {
                            fuelFee = new BigDecimal(oilFeeChild).multiply(new BigDecimal("2")); // 往返程双倍
                        }
                    }
                }
            } else {
                // 单程：使用原有逻辑
                if (dto.getFlight() != null) {
                    if (PassengerTypeEnums.ADULT.name().equals(passengerType)) {
                        if (StringUtils.isNotBlank(dto.getFlight().getPriceAdult())) {
                            ticketAmount = new BigDecimal(dto.getFlight().getPriceAdult());
                        }
                        if (StringUtils.isNotBlank(dto.getFlight().getAmtAdultAirPortFee())) {
                            taxFee = new BigDecimal(dto.getFlight().getAmtAdultAirPortFee());
                        }
                        if (StringUtils.isNotBlank(dto.getFlight().getOilFeeAdult())) {
                            fuelFee = new BigDecimal(dto.getFlight().getOilFeeAdult());
                        }
                    } else if (PassengerTypeEnums.CHILD.name().equals(passengerType)) {
                        if (StringUtils.isNotBlank(dto.getFlight().getPriceChild())) {
                            ticketAmount = new BigDecimal(dto.getFlight().getPriceChild());
                        }
                        String amtChildAirPortFee = dto.getFlight().getAmtChildAirPortFee();
                        String oilFeeChild = dto.getFlight().getOilFeeChild();
                        if (StringUtils.isNotBlank(amtChildAirPortFee)) {
                            taxFee = new BigDecimal(amtChildAirPortFee);
                        }
                        if (StringUtils.isNotBlank(oilFeeChild)) {
                            fuelFee = new BigDecimal(oilFeeChild);
                        }
                    }
                }
            }
            orderSubEntity.setTicketAmount(ticketAmount);
            orderSubEntity.setTaxFee(taxFee);
            orderSubEntity.setFuelFee(fuelFee);
            orderSubEntity.setCreateTime(new Date());
            orderSubEntity.setUpdateTime(new Date());
            orderSubs.add(orderSubEntity);
        }
        //创建联系人
        PassengerEntity contact = new PassengerEntity();
        contact.setId(contactId);
        contact.setVersion(0L);
        contact.setMemberId(dto.getMemberId());
        contact.setName(dto.getContact().getContactName());
        contact.setPhone(dto.getContact().getContactMobile());
        contact.setUseType(PassengerUseTypeEnums.CONTACT.name());
        contact.setCreateTime(new Date());
        contact.setUpdateTime(new Date());
        passagers.add(contact);
        // 处理航班信息
        if (Boolean.TRUE.equals(dto.getIsRoundTrip())) {
            // 往返程：创建去程和回程航班信息
            List<FlightInfoEntity> flights = new ArrayList<>();

            // 创建去程航班信息
            if (dto.getDeparture() != null) {
                FlightInfoEntity departureFlight = buildFlightInfo(dto.getDeparture(), order.getId());
                flights.add(departureFlight);
            }

            // 创建回程航班信息
            if (dto.getReturn_() != null) {
                FlightInfoEntity returnFlight = buildFlightInfo(dto.getReturn_(), order.getId());
                flights.add(returnFlight);
            }

            orderService.saveOrderAndRelationTables(order, orderSubs, passagers, flights);
        } else {
            // 单程：创建单个航班信息
            FlightInfoEntity flight = buildFlightInfo(dto.getFlight(), order.getId());
            orderService.saveOrderAndRelationTables(order, orderSubs, passagers, flight);
        }
        logger.info("系统订单创建成功");
        return order;
    }

    /**
     * 构建航班信息实体
     * @param flightDto 航班信息DTO
     * @param orderId 订单ID
     * @return 航班信息实体
     */
    private FlightInfoEntity buildFlightInfo(TicketOrderFlightRequestDTO flightDto, String orderId) {
        FlightInfoEntity flight = new FlightInfoEntity();
        flight.setId(RedisIdUtils.getId());
        flight.setVersion(0L);
        flight.setFlightNo(flightDto.getFlightNo());

        try {
            flight.setDepartureDate(DateUtils.sdfDateOnly.parse(flightDto.getDeptTime().substring(0,10)));
            flight.setDepartureTime(DateUtils.sdfDateTime.parse(flightDto.getDeptTime()));
            flight.setArrivalDate(DateUtils.sdfDateOnly.parse(flightDto.getArrTime().substring(0,10)));
            flight.setArrivalTime(DateUtils.sdfDateTime.parse(flightDto.getArrTime()));
        } catch (ParseException e) {
            logger.error("创建航班订单信息时时间转换错误", e);
        }

        Map<String,String> airPortAndName = cityAirportService.queryAllAirPort();
        Map<String,String> airPortAndCityName = cityAirportService.queryAllAirPortInCityName();

        flight.setAirCode(CheckUtils.isEmpty(flightDto.getAirCode()) ? "MU" : flightDto.getAirCode());
        flight.setDeptAirport(flightDto.getDeptAirport());
        flight.setDepartureCityName(airPortAndCityName.get(flightDto.getDeptAirport()));
        flight.setDepartureAirportName(airPortAndName.get(flightDto.getDeptAirport()));
        flight.setDepartureAirportTerminal(flightDto.getDepTerminal());

        flight.setArrAirport(flightDto.getArrAirport());
        flight.setArrivalCityName(airPortAndCityName.get(flightDto.getArrAirport()));
        flight.setArrivalAirportName(airPortAndName.get(flightDto.getArrAirport()));
        flight.setArrivalAirportTerminal(flightDto.getArrTerminal());
        flight.setCabinClass(flightDto.getCabinCls());
        flight.setFlightModel(flightDto.getPlaneType());
        flight.setProductCode(flightDto.getProductCode());
        flight.setMeal(flightDto.getMeal());
        flight.setStop(flightDto.getStop());
        flight.setCreateTime(new Date());
        flight.setUpdateTime(new Date());
        flight.setOrderId(orderId);
        flight.setShare(flightDto.getShare());
        flight.setOperateFlightNo(flightDto.getOperateFlightNo());

        return flight;
    }

    public RequestTicketEntity buildRequestTicket(OrderEntity orderEntity){
        String requestChannel = ticketServiceRouteFactory.tikcetServiceRouter();
        RequestTicketEntity requestTicketEntity = new RequestTicketEntity();
        requestTicketEntity.setId(RedisIdUtils.getId());
        requestTicketEntity.setVersion(0L);
        requestTicketEntity.setBussinessId(orderEntity.getId());
        requestTicketEntity.setBussinessType(RequestTicketMethodEnums.ORDER.name());
        requestTicketEntity.setAmount(orderEntity.getOrderAmount());
        requestTicketEntity.setRequestChannel(requestChannel);
        requestTicketEntity.setRequestMethod("ORDER");
        requestTicketEntity.setStatus(RequestTicketStatusEnums.INIT.name());
        requestTicketEntity.setRequestTime(new Date());
        requestTicketEntity.setRequestChannelOrderNo(RedisIdUtils.getId());
        String merchantNo = ConfigUtils.requestB2bMerchantNo(requestChannel);
        requestTicketEntity.setMerchantNo(merchantNo);
        requestTicketEntity.setCreateTime(new Date());
        requestTicketEntity.setUpdateTime(new Date());
        return requestTicketEntity;
    }

    public ImmutablePair<OrderEntity,RequestTicketEntity> updateOrder(TicketOrderResponseBean bean, OrderEntity order, RequestTicketEntity requestTicketEntity ){
        if(!CheckUtils.isEmpty(bean)){
            if(ORDER_SUCCESS.equals(bean.getOrderStatus())){
                order.setOrderAmount(bean.getPayPrice());
                order.setAirOrderNo(bean.getAirOrderNo());
                order.setStatus(OrderStatusEnums.ORDER_SUCCESS.name());

                Date payTimeLimit = bean.getPayTimeLimit();
                if (null == payTimeLimit) {
                    int minutes = ConfigUtils.getDefaultValue("SPACE_TRAVEL_ORDER_TIME_OURT_MINUTES", 30);
                    payTimeLimit = DateUtils.addMinute(new Date(), minutes);
                }
                order.setTimeoutTime(payTimeLimit);
                requestTicketEntity.setStatus(RequestTicketStatusEnums.SUCCESS.name());
            }else{
                order.setStatus(OrderStatusEnums.FAIL.name());
                requestTicketEntity.setStatus(RequestTicketStatusEnums.FAIL.name());
            }
            requestTicketEntity.setChannelRespTime(new Date());
            requestTicketEntity.setChannelRetCode(bean.getOrderStatus());
            requestTicketEntity.setChannelRetMsg(bean.getOrderMsg());
            requestTicketEntity.setChannelStatus(bean.getOrderStatus());
            requestTicketEntity.setChannleResponseOrderNo(bean.getAirOrderNo());
        }else{
            order.setStatus(OrderStatusEnums.FAIL.name());
            requestTicketEntity.setStatus(RequestTicketStatusEnums.FAIL.name());
        }
        order.setUpdateTime(new Date());
        requestTicketEntity.setUpdateTime(new Date());
        orderService.updateOrderAndRequestTicket(order,requestTicketEntity);
        return new ImmutablePair<>(order,requestTicketEntity);
    }



}

package com.yeepay.spacetravel.service.core.biz.impl.refund;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.date.DateUtils;
import com.yeepay.spacetravel.common.util.exception.OperationException;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.common.util.redis.RedisIdUtils;
import com.yeepay.spacetravel.common.util.redis.RedisLockUtils;
import com.yeepay.spacetravel.common.util.storage.StorageFileUtils;
import com.yeepay.spacetravel.common.util.validator.BeanValidator;
import com.yeepay.spacetravel.service.core.biz.payRefund.PayRefundBiz;
import com.yeepay.spacetravel.service.core.biz.refund.RefundBiz;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPassagerBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketRefundResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.TicketInterFaceBiz;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.TicketInterfaceBizFactory;
import com.yeepay.spacetravel.service.core.entity.bank.RequestBankEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderSubEntity;
import com.yeepay.spacetravel.service.core.entity.passenger.PassengerEntity;
import com.yeepay.spacetravel.service.core.entity.refund.RefundOrderEntity;
import com.yeepay.spacetravel.service.core.entity.refund.RefundOrderSubEntity;
import com.yeepay.spacetravel.service.core.entity.ticket.RequestTicketEntity;
import com.yeepay.spacetravel.service.core.service.order.OrderService;
import com.yeepay.spacetravel.service.core.service.order.OrderSubService;
import com.yeepay.spacetravel.service.core.service.passenger.PassengerService;
import com.yeepay.spacetravel.service.core.service.refund.RefundOrderService;
import com.yeepay.spacetravel.service.core.service.ticket.RequestTicketService;
import com.yeepay.spacetravel.service.facade.dto.refund.request.RefundOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.enums.order.OrderStatusEnums;
import com.yeepay.spacetravel.service.facade.enums.refund.RefundOrderStatuEnums;
import com.yeepay.spacetravel.service.facade.enums.refund.RefundTypeEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketMethodEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketStatusEnums;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.utils.lock.impl.RedisLock;
import com.yeepay.yop.sdk.utils.JsonUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Encoder;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/15 16:08
 * @Description:
 */
@Service
public class RefundBizImpl implements RefundBiz {
    private Logger logger = LoggerFactory.getLogger(RefundBizImpl.class);
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderSubService orderSubService;
    @Autowired
    private RefundOrderService refundOrderService;
    @Autowired
    private PassengerService passengerService;
    @Autowired
    private RequestTicketService requestTicketService;
    @Autowired
    private PayRefundBiz payRefundBiz;

    private static String SUCCESS = "R10001";

    @Override
    public BaseResult<String> refundOrder(RefundOrderRequestDTO dto) {
        logger.info("请求退款的参数为{}", JsonUtils.toJsonString(dto));
        BeanValidator.validate(dto);
        //判断参数必填性
        checkParam(dto);
        //逻辑处理
        RedisLock redisLock = null;
        try{
            //判断order
            OrderEntity order = checkOrder(dto);
            redisLock = RedisLockUtils.lock(order.getId(), 20);
            //校验退票乘机人是否已经发起过退票，退票失败的可以退
            ImmutablePair<List<PassengerEntity>,List<OrderSubEntity>> passengerAndSubs = checkPassenger(dto,order);
            List<PassengerEntity> passengers = passengerAndSubs.getLeft();
            List<OrderSubEntity> orderSubs = passengerAndSubs.getRight();

            //获取文件流
            ImmutablePair<String,String> file = getFile(dto.getImgUrl());
            //判断是否需要进行退票

            //创建订单
            ImmutablePair<RefundOrderEntity,List<RefundOrderSubEntity>> orderAndSubs = buildRefundOrder(dto,orderSubs);
            RefundOrderEntity refundOrder = orderAndSubs.getLeft();
            List<RefundOrderSubEntity> subs = orderAndSubs.getRight();

            //出票成功
            RequestTicketEntity payTicket = requestTicketService.queryRequestTicket(order.getId(),RequestTicketMethodEnums.PAY_TICKET.name(),new String[]{RequestTicketStatusEnums.SUCCESS.name(),RequestTicketStatusEnums.PAY_SUCCESS.name()});

            if(!CheckUtils.isEmpty(payTicket)){
                logger.info("该订单已经请求过出票机构,先进行出票机构的退票处理");
                //创建退票请求记录
                RequestTicketEntity requestTicketEntity = new RequestTicketEntity(payTicket.getRequestChannel(),RequestTicketMethodEnums.REFUND_TICKET.name(), refundOrder.getId(),RequestTicketMethodEnums.REFUND_TICKET.name(),new BigDecimal("0"),RedisIdUtils.getId());
                requestTicketService.add(requestTicketEntity);
                //请求退票接口
                TicketInterFaceBiz biz = TicketInterfaceBizFactory.getService(payTicket.getRequestChannel());
                TicketRefundResponseBean bean = biz.refundTicket(payTicket.getBussinessId(),payTicket.getRequestChannelOrderNo(),requestTicketEntity.getRequestChannelOrderNo(),passengers,dto.getRefundFlightReason(),file);
                //判断退票受理结果
                ImmutablePair<RefundOrderEntity,RequestTicketEntity> pair = biz.refundTicketAccpetForUpdateRefundOrder(bean,refundOrder,subs,requestTicketEntity);
                refundOrder = pair.getLeft();
                if(RefundOrderStatuEnums.T_R_P.name().equals(refundOrder.getStatus())){
                    return BaseResult.success(refundOrder.getId());
                }else{
                    String message = ReturnCode.getRetMsg(ReturnCode.ORDER_FAIL);
                    if(!CheckUtils.isEmpty(pair.getRight().getChannelRetMsg())){
                        message = pair.getRight().getChannelRetMsg();
                        int index = message.indexOf("-");
                        if(index>0){
                            message = message.substring(index+1,message.length());
                        }
                    }
                    BaseResult baseResult =  BaseResult.fail(ReturnCode.ORDER_FAIL,message);
                    baseResult.setData(refundOrder.getId());
                    return baseResult;
                }
            }else{
                logger.info("该订单未请求过出票机构进行出票,直接进行用户退款");
                refundOrderService.updateRefundOrderFundReceive(refundOrder.getId(),order.getOrderAmount());
                BaseResult baseResult = payRefundBiz.refundAmountForMember(refundOrder);
                baseResult.setData(refundOrder.getId());
                return baseResult;

            }


        }catch (OperationException e){
            logger.info("小程序退票过程中出现系统内部异常",e);
            return BaseResult.fail(e.getDefineCode(),e.getMessage());
        } catch (Exception e) {
            logger.info("进行退票下单过程中出现未知异常",e);
            return BaseResult.fail(ReturnCode.FAIL,e.getMessage());
        }finally {
            if(null!=redisLock){
                redisLock.unlock();;
            }
        }
    }

    @Override
    public void systemBuildCallRefund(OrderEntity order) {
        RefundOrderRequestDTO dto  = new RefundOrderRequestDTO();
        dto.setMemberId(order.getMemberId());
        dto.setIsVoluntary("1");
        dto.setOrderId(order.getId());
        dto.setRefundType(RefundTypeEnums.TICKET_FAIL.name());
        List<OrderSubEntity> subs = orderSubService.queryOrderSubsByOrderId(order.getId());
        List<String> passengerIds = new ArrayList<>();
        for (OrderSubEntity sub : subs) {
            passengerIds.add(sub.getPassengerId());
        }
        dto.setPassengerIds(passengerIds);
        this.refundOrder(dto);
    }


    public void checkParam(RefundOrderRequestDTO dto){
        if(dto.getIsVoluntary()=="0"){
            if(CheckUtils.isEmpty(dto.getRefundFlightReason()) || CheckUtils.isEmpty(dto.getReasonDetail())||CheckUtils.isEmpty(dto.getImgUrl())){
                throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"必填参数异常");
            }
        }
    }

    public OrderEntity checkOrder(RefundOrderRequestDTO dto){
        OrderEntity order = orderService.get(dto.getOrderId());
        if(!dto.getMemberId().equals(order.getMemberId())){
            logger.info("订单不属于该会员{},订单会员{}",dto.getMemberId(),order.getMemberId());
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"非该会员订单不允许操作");
        }
        if(!OrderStatusEnums.PAY_SUCCESS.name().equals(order.getStatus())
                &&!OrderStatusEnums.TICKET_SUCCESS.name().equals(order.getStatus())
                &&!OrderStatusEnums.TICKET_FAIL.name().equals(order.getStatus())){
            logger.info("订单状态异常不允许退款{}",order.getStatus());
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"订单状态异常不允许操作退款");
        }
        return order;
    }

    public ImmutablePair<List<PassengerEntity>,List<OrderSubEntity>> checkPassenger(RefundOrderRequestDTO dto, OrderEntity order){
        List<OrderSubEntity> orderSubs = orderSubService.queryOrderSubsByOrderId(order.getId());
        if(CollectionUtils.isEmpty(orderSubs)){
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"订单不存在子单");
        }
        //判断乘机人是否都在
        List<String> dataBasePassenger = new ArrayList<>();
        for (OrderSubEntity orderSub : orderSubs) {
            dataBasePassenger.add(orderSub.getPassengerId());
        }
        for (String passengerId : dto.getPassengerIds()) {
                if(!dataBasePassenger.contains(passengerId)){
                    throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"订单中不包含乘机人："+passengerId);
                }
        }

        //查询乘机人是否已经退票了
        String refundStatus[] = new String[]{RefundOrderStatuEnums.INIT.name(),RefundOrderStatuEnums.T_R_P.name()
                ,RefundOrderStatuEnums.T_R_S.name(),RefundOrderStatuEnums.R_RE_S.name()
                ,RefundOrderStatuEnums.SUCCESS.name()};
        List<RefundOrderEntity> refundOrders = refundOrderService.queryByPassengerIdsAndOrderIdAndStatus(dto.getPassengerIds(),order.getId(),refundStatus);
        if(CollectionUtils.isNotEmpty(refundOrders)){
            logger.info("选中的乘机人已经退票,请选择其他乘机人");
            throw OperationException.newInstantce(ReturnCode.PARAM_ERROR,"选中的乘机人已经退票,请选择其他乘机人");
        }
        List<PassengerEntity> passengers = new ArrayList<>();
        List<OrderSubEntity> subs = new ArrayList<>();
        for (OrderSubEntity orderSub : orderSubs) {
            if(dto.getPassengerIds().contains(orderSub.getPassengerId())){
                PassengerEntity passenger = passengerService.get(orderSub.getPassengerId());
                passengers.add(passenger);
                subs.add(orderSub);
            }


        }
        return new ImmutablePair<>(passengers,subs);
    }

    public ImmutablePair<RefundOrderEntity,List<RefundOrderSubEntity>> buildRefundOrder(RefundOrderRequestDTO dto, List<OrderSubEntity> orderSubs){
        RefundOrderEntity refundOrder = new RefundOrderEntity();
        refundOrder.setId(RedisIdUtils.getId());
        refundOrder.setVersion(0L);
        refundOrder.setRefundOrderNo(RedisIdUtils.getId());
        refundOrder.setOrderId(dto.getOrderId());
        refundOrder.setRefundReason(dto.getRefundFlightReason());
        refundOrder.setRefundFile(dto.getImgUrl());
        refundOrder.setStatus(RefundOrderStatuEnums.INIT.name());
        refundOrder.setRefundOrderTime(new Date());
        refundOrder.setRefundComplateTime(new Date());
        refundOrder.setRefundType(dto.getRefundType());
        refundOrder.setReason(dto.getReasonDetail());
        refundOrder.setFlightReason(dto.getRefundFlightReason());
        refundOrder.setCreateTime(new Date());
        refundOrder.setUpdateTime(new Date());
        List<RefundOrderSubEntity> subs = new ArrayList<>();
        for (OrderSubEntity orderSubEntity : orderSubs) {
            RefundOrderSubEntity sub = new RefundOrderSubEntity();
            sub.setId(RedisIdUtils.getId());
            sub.setVersion(0L);
            sub.setRefundOrderSubNo(RedisIdUtils.getId());
            sub.setRefundOrderId(refundOrder.getId());
            sub.setTicketNo(orderSubEntity.getTicketNo());
            sub.setPassengerId(orderSubEntity.getPassengerId());
            sub.setCreateTime(new Date());
            sub.setUpdateTime(new Date());
            subs.add(sub);
        }
        refundOrderService.saveOrderAndSub(refundOrder,subs);
        return new ImmutablePair<>(refundOrder,subs);
    }

    public ImmutablePair<String,String > getFile(String url) throws IOException {
        if(!CheckUtils.isEmpty(url)){
            int i = url.indexOf("img/");
            if(i>0){
                url = url.substring(i,url.length());
            }
            String fileName = url.substring(4,url.length());
            logger.info("退款附近在云存储上的url为{}",url);
            InputStream inputStream = StorageFileUtils.downloadYpFile(url);
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int n = 0;
            while (-1 != (n = inputStream.read(buffer))) {
                output.write(buffer, 0, n);
            }
            byte[] bytes = output.toByteArray();
            String result = (new BASE64Encoder()).encodeBuffer(bytes);
            logger.info("base64加密后的文件流数据为{}",result);
            result = result.replace("+","updateFilePlaceholder");
            return new ImmutablePair<>(fileName,result);
        }
        return null;
    }




}

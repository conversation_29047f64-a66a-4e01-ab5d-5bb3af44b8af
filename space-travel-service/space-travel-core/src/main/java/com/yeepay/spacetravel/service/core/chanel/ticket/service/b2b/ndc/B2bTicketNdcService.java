package com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.ndc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.encDec.AirB2bDigesHmactUtil;
import com.yeepay.spacetravel.common.util.encDec.AirB2bTokenEcryptUtils;
import com.yeepay.spacetravel.common.util.env.IpServerStatisticUtils;
import com.yeepay.spacetravel.common.util.http.HttpClient;
import com.yeepay.spacetravel.service.core.bean.flight.QueryFlightInfoBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.Policy;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.RealPriceRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketContactBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketFlightBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderQueryRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketOrderResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPassagerBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPayRequestBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.bean.TicketPayResponseBean;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.AbstractB2bTicketService;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketFactory;
import com.yeepay.spacetravel.service.core.chanel.ticket.service.b2b.B2bTicketService;
import com.yeepay.spacetravel.service.core.entity.RequestInfoEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.service.RequestInfoService;
import com.yeepay.spacetravel.service.core.service.flight.FlightInfoService;
import com.yeepay.spacetravel.service.core.service.refund.RefundOrderService;
import com.yeepay.spacetravel.service.facade.dto.order.request.TicketOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.enums.common.SpaceTravelCommonStatusEnums;
import com.yeepay.spacetravel.service.facade.enums.flight.AirCodeEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.RequestTicketMethodEnums;
import com.yeepay.spacetravel.service.facade.enums.tikcet.TicketServiceEnums;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/30 上午11:04
 */
@Service
public class B2bTicketNdcService extends AbstractB2bTicketService implements B2bTicketService {

    private Logger logger = LoggerFactory.getLogger(B2bTicketNdcService.class);


    private static final String airAccountPwd = "007yeepay";
    private static final String perfix = "##";
    //"https://airt.yeepay.com/air-ticket-api/airb2c/request.action";
    //测试地址：http://ycetest.yeepay.com:30564/air-ticket-api/airb2c/request.action
    //生产地址：https://airt.yeepay.com/air-ticket-api/airb2c/request.action
//    private static final String COMMON_URL = "http://ycetest.yeepay.com:30564/air-ticket-api/airb2c/request.action";

    private static final String QUREY_FLIGHT_SUCCESS_STATUS = "GETPRICE_SUCCESS";

    @Autowired
    private RequestInfoService requestInfoService;
    @Autowired
    private RefundOrderService refundOrderService;
    @Autowired
    private FlightInfoService flightInfoService;

    B2bTicketNdcService() {
        B2bTicketFactory.register("B2CYPMFNDC", this);
        B2bTicketFactory.register("B2CYPMUNDC", this);
    }

    @Override
    public List<Policy> queryRealPrice(QueryFlightInfoBean bean) {

        RealPriceRequestBean requestBean = buildRequestBean(bean);
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(requestBean.getRequestId(), RequestTicketMethodEnums.QUERY_FLIGHT.name(), getChannel(), requestBean, SpaceTravelCommonStatusEnums.INIT.name());

        //请求出票渠道
        try {
            List<NameValuePair> requestParm = buildParam(requestBean);
            logger.info("请求自动出票航班查询接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出获取航班票耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票航班查询接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票航班查询未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
            JSONObject responseDataJson = JSONObject.parseObject(responseData);
            if (QUREY_FLIGHT_SUCCESS_STATUS.equals(responseDataJson.getString("orderStatus"))) {
                logger.info("请求航班查询成功");
                JSONArray policyArray = (JSONArray) responseDataJson.get("policy");
                List<Policy> result = new ArrayList<>();
                for (Object o : policyArray) {
                    String ploicyString = JSONUtils.toJsonString(o);
                    Policy policy = JSONUtils.jsonToBean(ploicyString, Policy.class);
                    policy.setAircode(bean.getAirCode());
                    result.add(policy);
                }
                return result;
            }

        } catch (Exception e) {
            logger.error("请求自动出票查航班出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;
    }


    /**
     * 构建自动出票请求参数模板
     *
     * @param requestBean
     * @return
     */
    public List<NameValuePair> buildParam(Object requestBean) {
        List<NameValuePair> list = new ArrayList<>();
        try {
            Class<?> objectClass = requestBean.getClass();
            Field[] fields = objectClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getName().equals("uploadFileInfo")) {
                    String value = String.valueOf(field.get(requestBean));
                    if (CheckUtils.isEmpty(value) || "null".equals(value)) {
                        continue;
                    }
                }
                BasicNameValuePair param = new BasicNameValuePair(field.getName(), String.valueOf(field.get(requestBean)));
                list.add(param);
            }
        } catch (Exception e) {
            logger.info("拼装请求参数时出现未知异常");
            return null;
        }
        return list;
    }

    public RealPriceRequestBean buildRequestBean(QueryFlightInfoBean info) {
        RealPriceRequestBean bean = new RealPriceRequestBean();
        bean.setCmd("NewRealPrice");
        bean.setCustomerSign(getMerchantCustomerSign());
        bean.setRequestId(info.getRequestChannelNo());
        bean.setAir2Code(info.getAirCode());
        bean.setAirChannel("www");
        bean.setDeptAirport(info.getDeptAirport());
        bean.setArrAirport(info.getArrAirport());
        bean.setDeptDate(info.getDeptDate());
        bean.setAdultNum(info.getAdultNum());
        bean.setChildNum(info.getChildNum());
        //构建hmac
        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("requestId");
        hmacList.add("air2Code");
        hmacList.add("airChannel");
        hmacList.add("deptAirport");
        hmacList.add("arrAirport");
        hmacList.add("deptDate");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        String key = getMerchantKey();
        logger.info("请求自动出票获取政策生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票获取政策生成hmac为{}", hmac);
        //构建token
        String tokenValue = bean.getRequestId() + perfix + airAccountPwd + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票获取政策token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票获取征程token的值为{}", token);
        bean.setHmac(hmac);
        bean.setToken(token);
        return bean;
    }

    private Map<String, String> getRequestB2bInfo() {
        Map<String, String> info = new HashMap<>();
        logger.info("当前处理逻辑所在环境是否为内测{}", IpServerStatisticUtils.getEnvironmentTest());
        if (IpServerStatisticUtils.getEnvironmentTest()) {
            info = ConfigUtils.requestB2bMerchantInfoForNc(getChannel());
        } else {
            info = ConfigUtils.requestB2bMerchantInfo(getChannel());
        }
        return info;
    }

    private String getChannel() {
        return TicketServiceEnums.AIRB2B.name();
    }

    private String getMerchantCustomerSign() {
        return getRequestB2bInfo().get("customerSign");
    }

    private String getMerchantKey() {
        return getRequestB2bInfo().get("key");
    }

    private String getMerchantLoginName() {
        return getRequestB2bInfo().get("loginName");
    }

    private String getMerchantPwd() {
        return getRequestB2bInfo().get("pwd");
    }

    private String getMerchantCommonUrl() {
        return getRequestB2bInfo().get("COMMON_URL");
    }


    @Override
    public TicketOrderResponseBean order(TicketOrderRequestDTO dto, String orderId, String requestChannelNo) {
        TicketOrderRequestBean requestOrderBean = buildRequestTicketBean(dto, getChannel(), requestChannelNo);
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(orderId, RequestTicketMethodEnums.ORDER.name(), getChannel(), requestOrderBean, SpaceTravelCommonStatusEnums.INIT.name());

        //请求下单
        try {
            List<NameValuePair> requestParm = buildParam(requestOrderBean);
            logger.info("请求自动出票下单接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出下单票耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票下单接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票下单接口未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());

            TicketOrderResponseBean bean = JSONUtils.jsonToBean(responseData, TicketOrderResponseBean.class);
            return bean;

        } catch (Exception e) {
            logger.error("请求自动出票下单出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
            return null;
        }
    }

    @Override
    public TicketPayResponseBean payTicket(String orderId, String requestTicketOrderId, BigDecimal payAmount, FlightInfoEntity flightInfo) {

        TicketPayRequestBean bean = buildPayTicketRequestBean(requestTicketOrderId, payAmount, getChannel(), flightInfo.getAirCode());
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(orderId, RequestTicketMethodEnums.PAY_TICKET.name(), getChannel(), bean, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            List<NameValuePair> requestParm = buildParam(bean);
            logger.info("请求自动出票支付出票接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出支付出票票耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票支付出票接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票支付出票接口未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
            logger.info("请求航司支付成功");
            TicketPayResponseBean responseBean = JSONUtils.jsonToBean(responseData, TicketPayResponseBean.class);
            return responseBean;

        } catch (Exception e) {
            logger.error("请求自动出票支付出票出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;
    }

    public TicketPayRequestBean buildPayTicketRequestBean(String requestId, BigDecimal payAmount, String requestChannel, String air2Code) {
        TicketPayRequestBean bean = new TicketPayRequestBean();
        String key = getMerchantKey();
        String merchantNo = getMerchantCustomerSign();

        bean.setCustomerSign(merchantNo);
        bean.setCustomerNumber(merchantNo.replace("YP", ""));
        String tokenValue = bean.getCustomerSign() + perfix + bean.getYeePayLoginName() + perfix + bean.getYeePayTrxPwd() + perfix + bean.getRequestId() + airAccountPwd + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票支付出票token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票支付出票token的值为{}", token);
        bean.setToken(token);
        bean.setRequestId(requestId);

        bean.setAir2Code(air2Code);
        bean.setAirAccount(air2Code);
        bean.setAirAccountPwd(air2Code);
        bean.setAirAccountType(air2Code);

        bean.setPayPrice(payAmount.toString());
        String loginName = getMerchantLoginName();
        bean.setYeePayLoginName(loginName);
        String pwd = getMerchantPwd();
        bean.setYeePayTrxPwd(pwd);

        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("token");
        hmacList.add("airAccount");
        hmacList.add("airAccountType");
        hmacList.add("requestId");
        hmacList.add("payPrice");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        logger.info("请求自动出票支付出票生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票支付出票生成hmac为{}", hmac);
        bean.setHmac(hmac);
        return bean;
    }

    public TicketOrderRequestBean buildRequestTicketBean(TicketOrderRequestDTO dto, String requestChannel, String requestChannelNo) {
        List<TicketFlightBean> flights = new ArrayList<>();
        TicketFlightBean flightBean = new TicketFlightBean();
        BeanUtils.copyProperties(dto.getFlight(), flightBean);
        flightBean.setDeptTime(dto.getFlight().getDeptTime().substring(11, 16));
        flightBean.setArrTime(dto.getFlight().getArrTime().substring(11, 16));
        flightBean.setPriceAdult((new BigDecimal(dto.getFlight().getPriceAdult()).add(new BigDecimal(dto.getFlight().getTaxFeeAdult()))).toString());
        flights.add(flightBean);


        List<TicketPassagerBean> passagers = new ArrayList<>();
        BeanUtils.copyListProperties(dto.getPassagers(), passagers, TicketPassagerBean.class);
        for (TicketPassagerBean passager : passagers) {
            if (StringUtils.isEmpty(passager.getEmail())) {
                passager.setEmail("<EMAIL>");
            }
        }

        TicketContactBean contactBean = new TicketContactBean();
        BeanUtils.copyProperties(dto.getContact(), contactBean);

        TicketOrderRequestBean bean = new TicketOrderRequestBean();
        String merchantNo = getMerchantCustomerSign();
        bean.setCustomerSign(merchantNo);
        bean.setCustomerNumber(merchantNo.replace("YP", ""));
        bean.setRequestId(requestChannelNo);
        bean.setAir2Code(CheckUtils.isEmpty(dto.getFlight().getAirCode()) ? AirCodeEnums.B2CYPMUNDC.name() : dto.getFlight().getAirCode());
        bean.setAirAccount(bean.getAir2Code());
        bean.setAirAccountPwd(bean.getAir2Code());
        bean.setAirAccountType(bean.getAir2Code());
        bean.setFlight(JSONUtils.toJsonString(flights));
        bean.setPassenger(JSONUtils.toJsonString(passagers));
        bean.setContact(JSONUtils.toJsonString(contactBean));
        bean.setYeepayLoginName(getMerchantLoginName());
        bean.setYeepayTrxPwd(getMerchantPwd());
        bean.setPriceType("EVERY_DISCOUNT");

        String key = getMerchantKey();

        String tokenValue = bean.getYeepayLoginName() + perfix + bean.getYeepayTrxPwd() + perfix + bean.getRequestId() + airAccountPwd + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票下单token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票下单token的值为{}", token);
        bean.setToken(token);


        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("token");
        hmacList.add("requestId");
        hmacList.add("air2Code");
        hmacList.add("airAccount");
        hmacList.add("flight");
        hmacList.add("passenger");
        hmacList.add("contact");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        logger.info("请求自动出票下单生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票下单生成hmac为{}", hmac);
        bean.setHmac(hmac);
        return bean;
    }

    @Override
    public TicketOrderResponseBean queryOrder(String orderId,String requestId,FlightInfoEntity flightInfo) {

        TicketOrderQueryRequestBean bean = buildOrderQueryBean(requestId, flightInfo.getAirCode());
        //创建请求参数记录
        RequestInfoEntity requestInfoEntity = requestInfoService.buildRequestInfo(requestId, RequestTicketMethodEnums.QUERY_ORDER.name(), getChannel(), bean, SpaceTravelCommonStatusEnums.INIT.name());
        try {
            List<NameValuePair> requestParm = buildParam(bean);
            logger.info("请求自动出票订单查询接口参数为{}", requestParm);
            Long startTime = System.currentTimeMillis();
            JSONObject jsonObject = HttpClient.sendHttpPostJson(getMerchantCommonUrl(), null, requestParm);
            Long endTime = System.currentTimeMillis();
            logger.info("请求自动出票订单查询耗时{}", startTime - endTime);
            String responseData = (String) jsonObject.get("responseData");
            logger.info("请求自动出票订单查询接口返回结果为{}", responseData);
            //返回结果解析
            if (CheckUtils.isEmpty(responseData)) {
                logger.info("请求自动出票订单查询接口未返回结果");
                responseData = "null";
                requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
                return null;
            }
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), responseData, SpaceTravelCommonStatusEnums.SUCCESS.name());
            logger.info("请求航司支付成功");
            TicketOrderResponseBean responseBean = JSONUtils.jsonToBean(responseData, TicketOrderResponseBean.class);
            return responseBean;

        } catch (Exception e) {
            logger.error("请求自动出票订单查询接口出现位置异常", e);
            requestInfoService.updateRequestInfo(requestInfoEntity.getId(), e.getMessage(), SpaceTravelCommonStatusEnums.SUCCESS.name());
        }
        return null;

    }

    public TicketOrderQueryRequestBean buildOrderQueryBean(String requestId,String airCode) {
        TicketOrderQueryRequestBean bean = new TicketOrderQueryRequestBean();
        String key = getMerchantKey();
        String merchantNo = getMerchantCustomerSign();

        bean.setRequestId(requestId);
        bean.setAirAccount(airCode);
        bean.setAirAccountPwd(airCode);
        bean.setAirAccountType(airCode);
        bean.setCustomerSign(merchantNo);
        bean.setCustomerNumber(merchantNo);

        String tokenValue = bean.getCustomerNumber() + perfix + bean.getCustomerNumber() + perfix + bean.getRequestId() + perfix + airAccountPwd + perfix + System.currentTimeMillis() + perfix + "true";
        logger.info("生成请求自动出票订单查询接口的token的原串是{}", tokenValue);
        String token = AirB2bTokenEcryptUtils.encryptAES(tokenValue, key);
        logger.info("生成请求自动出票订单查询接口的token的值为{}", token);
        bean.setToken(token);

        LinkedList<String> hmacList = new LinkedList<>();
        hmacList.add("cmd");
        hmacList.add("customerSign");
        hmacList.add("token");
        hmacList.add("airAccount");
        hmacList.add("airAccountType");
        hmacList.add("requestId");
        String hmacValue = buildEncryptValue(hmacList, bean, null);
        logger.info("请求自动出票支付订单查询接口生成hmac的原串为{}", hmacValue);
        String hmac = AirB2bDigesHmactUtil.hmacSign(hmacValue, key);
        logger.info("请求自动出票支付订单查询接口生成hmac为{}", hmac);
        bean.setHmac(hmac);
        return bean;
    }

}

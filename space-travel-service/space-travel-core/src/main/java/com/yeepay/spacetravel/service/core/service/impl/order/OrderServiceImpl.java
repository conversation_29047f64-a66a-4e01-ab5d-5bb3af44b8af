package com.yeepay.spacetravel.service.core.service.impl.order;

import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.date.DateUtils;
import com.yeepay.spacetravel.common.util.page.PageUtil;
import com.yeepay.spacetravel.common.util.page.Pagenation;
import com.yeepay.spacetravel.service.core.bean.condition.B2BQueryOrderListCondition;
import com.yeepay.spacetravel.service.core.bean.condition.OrderAndRefundPageQueryCondition;
import com.yeepay.spacetravel.service.core.bean.condition.OrderPageQueryCondition;
import com.yeepay.spacetravel.service.core.bean.orderQuery.B2BOrderQueryListBean;
import com.yeepay.spacetravel.service.core.bean.orderQuery.OrderAndRefundQueryListBean;
import com.yeepay.spacetravel.service.core.bean.orderQuery.OrderQueryListBean;
import com.yeepay.spacetravel.service.core.dao.order.OrderDao;
import com.yeepay.spacetravel.service.core.entity.bank.RequestBankEntity;
import com.yeepay.spacetravel.service.core.entity.flight.FlightInfoEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderEntity;
import com.yeepay.spacetravel.service.core.entity.order.OrderSubEntity;
import com.yeepay.spacetravel.service.core.entity.passenger.PassengerEntity;
import com.yeepay.spacetravel.service.core.entity.ticket.RequestTicketEntity;
import com.yeepay.spacetravel.service.core.service.bank.RequestBankService;
import com.yeepay.spacetravel.service.core.service.flight.FlightInfoService;
import com.yeepay.spacetravel.service.core.service.order.OrderService;
import com.yeepay.spacetravel.service.core.service.order.OrderSubService;
import com.yeepay.spacetravel.service.core.service.passenger.PassengerService;
import com.yeepay.spacetravel.service.core.service.ticket.RequestTicketService;
import com.yeepay.spacetravel.service.facade.enums.passager.PassengerUseTypeEnums;
import com.yeepay.yop.sdk.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("orderService")
public class OrderServiceImpl implements OrderService {
	private static final Logger log=LoggerFactory.getLogger(OrderServiceImpl.class);
	@Autowired 
	private OrderDao orderDao;
	@Autowired
	private OrderSubService orderSubService;
	@Autowired
	private PassengerService passengerService;
	@Autowired
	private FlightInfoService flightInfoService;
	@Autowired
	private RequestTicketService requestTicketService;
	@Autowired
	private RequestBankService requestBankService;
	
	@Override
	public void delete(String id) {
		this.orderDao.delete(id);
	}

	@Override
	public OrderEntity get(String id) {
		return this.orderDao.get(id);
	}

	@Override
	public List<OrderEntity> getAll() {
		return this.orderDao.getAll();
	}

	@Override
	public void add(OrderEntity orderEntity) {
		this.orderDao.add(orderEntity);
	}

	@Override
	public void update(OrderEntity orderEntity) {
		this.orderDao.update(orderEntity);
	}

	@Override
	public OrderEntity queryWaitPayOrder(String memberId, List<String> orderStatus, String flightNo, String depTime, List<String> certNums) {
		log.info("查询待支付订单的参数为{},{},{},{},{}",memberId, JsonUtils.toJsonString(orderStatus),flightNo,depTime,JsonUtils.toJsonString(certNums));
		Map<String,Object> map = new HashMap<>();
		map.put("memberId",memberId);
		map.put("flightNo",flightNo);
		map.put("departureTime",depTime);
		map.put("certNums",certNums);
		map.put("orderStatus",orderStatus);
		List<OrderEntity> orders = this.orderDao.query("queryWaitPayOrder",map);
		if(CollectionUtils.isNotEmpty(orders)){
			return orders.get(0);
		}
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void saveOrderAndRelationTables(OrderEntity order, List<OrderSubEntity> orderSubs, List<PassengerEntity> passagers, FlightInfoEntity flight) {
		if(!CheckUtils.isEmpty(order)){
			log.info("生成的订单信息为{}", JSONUtils.toJsonString(order));
			this.orderDao.add(order);
		}
		if(CollectionUtils.isNotEmpty(orderSubs)){
			for (OrderSubEntity orderSub : orderSubs) {
				log.info("生成的子订单信息为{}",JsonUtils.toJsonString(orderSub));
				orderSubService.add(orderSub);
			}
		}
		if(CollectionUtils.isNotEmpty(passagers)){
			for (PassengerEntity passager : passagers) {
				if(PassengerUseTypeEnums.PASSAGER.name().equals(passager.getUseType())){
					log.info("生成的乘机人信息为{}",JSONUtils.toJsonString(passager));
					passengerService.add(passager);
				}else{
					log.info("生成的联系人信息为{}",JSONUtils.toJsonString(passager));
					passengerService.add(passager);
				}
			}
		}
		if(!CheckUtils.isEmpty(flight)){
			log.info("生成的航班信息为{}",JSONUtils.toJsonString(flight));
			flightInfoService.add(flight);
		}

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void saveOrderAndRelationTables(OrderEntity order, List<OrderSubEntity> orderSubs, List<PassengerEntity> passagers, List<FlightInfoEntity> flights) {
		if(!CheckUtils.isEmpty(order)){
			log.info("生成的订单信息为{}", JSONUtils.toJsonString(order));
			this.orderDao.add(order);
		}
		if(CollectionUtils.isNotEmpty(orderSubs)){
			for (OrderSubEntity orderSub : orderSubs) {
				log.info("生成的子订单信息为{}",JsonUtils.toJsonString(orderSub));
				orderSubService.add(orderSub);
			}
		}
		if(CollectionUtils.isNotEmpty(passagers)){
			for (PassengerEntity passager : passagers) {
				if(PassengerUseTypeEnums.PASSAGER.name().equals(passager.getUseType())){
					log.info("生成的乘机人信息为{}",JSONUtils.toJsonString(passager));
					passengerService.add(passager);
				}else{
					log.info("生成的联系人信息为{}",JSONUtils.toJsonString(passager));
					passengerService.add(passager);
				}
			}
		}
		if(CollectionUtils.isNotEmpty(flights)){
			for (FlightInfoEntity flight : flights) {
				log.info("生成的航班信息为{}",JSONUtils.toJsonString(flight));
				flightInfoService.add(flight);
			}
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void updateOrderAndRequestTicket(OrderEntity order, RequestTicketEntity requestTicket) {
		this.orderDao.update(order);
		this.requestTicketService.update(requestTicket);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void updateOrderAndRequestBank(OrderEntity order, RequestBankEntity requestBank) {
		this.orderDao.update(order);
		this.requestBankService.update(requestBank);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void updateOrderAndRequestTicketAndSubs(OrderEntity order, RequestTicketEntity requestTicket, List<OrderSubEntity> subs) {
		this.orderDao.update(order);
		this.requestTicketService.update(requestTicket);
		if(CollectionUtils.isNotEmpty(subs)){
			for (OrderSubEntity sub : subs) {
				this.orderSubService.update(sub);
			}
		}

	}

	@Override
	public Pagenation<OrderQueryListBean> queryMyOrderListByPage(OrderPageQueryCondition condition) {
		if (condition.getPageNo() == null || condition.getPageSize() == null) {
			condition.initPage();
		}

		Pagenation<OrderQueryListBean> page = PageUtil.page(condition, () -> {
			Map<String, Object> paramMap = new HashMap<>(16);
			paramMap.put("statusList", condition.getStatusList());
			paramMap.put("status", condition.getStatus());
			paramMap.put("memberId", condition.getMemberId());
			orderDao.query("queryMyOrderList", paramMap);
		});

		return page;
	}

	@Override
	public List<OrderEntity> queryOrdersByOutTime(Date start, Date end) {
		Map<String,Date> map = new HashMap<>();
		map.put("start",start);
		map.put("end",end);
		return this.orderDao.query("queryOrdersByOutTime",map);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public void updateOrderAndPayEntities(OrderEntity order, List<RequestBankEntity> payEntities) {
		this.orderDao.update(order);
		if(CollectionUtils.isNotEmpty(payEntities)){
			for (RequestBankEntity payEntity : payEntities) {
				this.requestBankService.update(payEntity);
			}

		}
	}

	@Override
	public Pagenation<OrderAndRefundQueryListBean> queryMyOrderAndRefundByPage(OrderAndRefundPageQueryCondition condition) {
		if (condition.getPageNo() == null || condition.getPageSize() == null) {
			condition.initPage();
		}

		Pagenation<OrderAndRefundQueryListBean> page = PageUtil.page(condition, () -> {
			Map<String, Object> paramMap = new HashMap<>(16);
			paramMap.put("statusList", condition.getStatusList());
			paramMap.put("refundStatusList", condition.getRefundStatusList());
			paramMap.put("memberId", condition.getMemberId());
			orderDao.query("queryMyOrderAndRefundOrderList", paramMap);
		});

		return page;
	}

	@Override
	public Pagenation<B2BOrderQueryListBean> queryB2BOrderListByPage(B2BQueryOrderListCondition condition) {
		if (condition.getPageNo() == null || condition.getPageSize() == null) {
			condition.initPage();
		}

		Pagenation<B2BOrderQueryListBean> page = PageUtil.page(condition, () -> {
			Map<String, Object> paramMap = new HashMap<>(16);
			paramMap.put("status", condition.getStatus());
			Date createDate = condition.getCreateDate();
			if (createDate != null) {
				paramMap.put("createDate", DateUtils.sdfDateTime.format(createDate));
			}
			Date endDate = condition.getEndDate();
			if (endDate != null) {
				paramMap.put("endDate", DateUtils.sdfDateTime.format(endDate));
			}
			paramMap.put("order_no", condition.getOrderId());
			paramMap.put("orderBy", condition.getOrderby());
			paramMap.put("pnr", condition.getPNR());
			paramMap.put("id", condition.getId());
			orderDao.query("queryB2BOrderList", paramMap);
		});

		return page;
	}

	@Override
	public List<OrderEntity> queryUnfinishedStateOrders(Date start, Date end) {
		Map<String,Date> map = new HashMap<>();
		map.put("start",start);
		map.put("end",end);
		return this.orderDao.query("queryUnfinishedStateOrders",map);
	}


}

package com.yeepay.spacetravel.hessian.listener;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.spacetravel.common.util.env.IpServerStatisticUtils;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * @author: qingwang.gu
 * @Date: 2022/01/24 3:00 下午
 * @Description:
 */

public class SpaceTravelIpServerListener implements ServletContextListener {

    private static final Logger logger = LoggerFactory.getLogger(SpaceTravelIpServerListener.class);

    @Override
    public void contextDestroyed(ServletContextEvent arg0) {}

    @Override
    public void contextInitialized(ServletContextEvent arg0) {
        System.out.println("开始初始化");
        //读取测试环境配置文件
        boolean isEnvironmentTest = false;
        try {
            String environment = System.getProperty("deploy_env");
            logger.info("读取到的环境变量为{}",environment);
            if("staging".equals(environment)){
                isEnvironmentTest = true;
            }
        } catch (Exception e) {
            logger.info("IpServer区分生产/内测——判断是否为测试环境结果出现异常，将判断此为生产环境", e);
            isEnvironmentTest = false;
        }
        logger.info("IpServer区分生产/内测——判断是否为测试环境结果:"+isEnvironmentTest);
        IpServerStatisticUtils.setEnvironmentTest(isEnvironmentTest);
    }
}

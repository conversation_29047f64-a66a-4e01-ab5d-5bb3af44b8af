package com.yeepay.spacetravel.hessian;


import com.github.pagehelper.PageHelper;
import com.yeepay.spacetravel.common.util.env.SpaceTravelEnvUtils;
import com.yeepay.g3.bc.common.util.RedisIdUtil;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.smartcache.utils.RedisClientUtils;
import com.yeepay.spacetravel.hessian.listener.SpaceTravelIpServerListener;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Properties;

/**
 * @author: qingwang.gu
 * @Date: 2022/10/10 17:16
 * @Description:
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.yeepay.spacetravel.hessian",
        "com.yeepay.spacetravel.service.core","com.yeepay.spacetravel.common.util"})

@ImportResource({"classpath:spacetravel-service-mybatis-config.xml"})
@EnableSoa
@EnableTransactionManagement
@EnableAsync
public class SpaceTravelServiceApplication {
    static {
        System.setProperty("appname", "space-travel-service-hessian");
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
        RedisClientUtils.init();
        //分布式id
        RedisIdUtil.init();
        SpaceTravelEnvUtils.init();

    }


    public static void main(String[] args) {
        System.setProperty("javax.xml.parsers.DocumentBuilderFactory","com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl");
        SpringApplication.run(SpaceTravelServiceApplication.class, args);
    }

    @Bean
    public PageHelper pageHelper(){
        PageHelper pageHelper = new PageHelper();
        Properties properties = new Properties();
        properties.setProperty("pageSizeZero","true");
        properties.setProperty("offsetAsPageNum","true");
        properties.setProperty("rowBoundsWithCount","true");
        properties.setProperty("reasonable","true");
        //配置mysql数据库的方言
        properties.setProperty("dialect","mysql");
        pageHelper.setProperties(properties);
        return pageHelper;
    }

    @Bean
    public ServletListenerRegistrationBean configContextListener() {
        ServletListenerRegistrationBean bean = new ServletListenerRegistrationBean();
        bean.setListener(new SpaceTravelIpServerListener());
        return bean;
    }

}

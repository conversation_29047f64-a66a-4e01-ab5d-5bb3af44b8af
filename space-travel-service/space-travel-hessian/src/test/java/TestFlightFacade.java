
import base.BaseJunitTest;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.spacetravel.common.util.date.DateUtils;
import com.yeepay.spacetravel.service.core.biz.flight.FlightBiz;
import com.yeepay.spacetravel.service.facade.dto.b2b.refund.B2bRefundOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryB2BRuleRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryFlightInfoDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryB2BFlightInfoDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.QueryB2BRuleResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BContactDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BCreateOrderRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BOrderPayRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BOrderQueryDetailRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BOrderQueryListRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.order.request.B2BPassengerDTO;
import com.yeepay.spacetravel.service.facade.enums.refund.RefundTypeEnums;
import com.yeepay.spacetravel.service.facade.facade.b2b.refund.B2BRefundFacade;
import com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade;
import com.yeepay.spacetravel.service.facade.facade.order.OrderFacade;
import com.yeepay.spacetravel.service.facade.facade.order.OrderQueryFacade;
import com.yeepay.spacetravel.service.facade.facade.timer.b2b.B2bTicketQueryTimerFacade;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.facade.result.b2b.B2bBaseResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: qingwang.gu
 * @Date: 2023/01/04 16:42
 * @Description:
 */
public class TestFlightFacade extends BaseJunitTest {
    @Autowired
    private FlightFacade flightFacade;
    @Autowired
    private FlightBiz flightBiz;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private OrderQueryFacade orderQueryFacade;
    @Autowired
    private B2BRefundFacade b2BRefundFacade;
    @Autowired
    private B2bTicketQueryTimerFacade b2bTicketQueryTimerFacade;
    @Test
    public void test(){
        QueryFlightInfoDTO dto = new QueryFlightInfoDTO();
        dto.setDeptDate("2023-09-21");
//        dto.setDeptCityCode("610100");
//        dto.setArrCityCode("440300");
        dto.setDeptAirport("110000");
        dto.setArrAirport("310000");
        dto.setAdultNum("1");
        dto.setChildNum("0");
        BaseResult<List<FlightInfoResponseDTO>> baseResult= flightFacade.queryFlight(dto);
        System.out.println(JSONUtils.toJsonString(baseResult));
    }

    @Test
    public void tes31t(){
        QueryB2BFlightInfoDTO dto = new QueryB2BFlightInfoDTO();
//        dto.setDeptDate("2024-04-20");
//        dto.setDeptAirport("SIN");
//        dto.setArrAirport("HKD");
//        dto.setAirCode("SQ");
//        dto.setAdultNum("1");
//        dto.setChildNum("0");
//        dto.setCabinType("F");
//        dto.setId("1");

        dto.setDeptDate("2024-05-29");
        dto.setDeptAirport("PVG");
        dto.setArrAirport("NRT");
        dto.setAirCode("MU");
        dto.setAdultNum("1");
        dto.setChildNum("0");
        dto.setId("1");
        dto.setCabinType("Y");

        B2bBaseResult<List<FlightInfoResponseDTO>> baseResult= flightFacade.queryB2BFlight(dto);
        System.out.println(JSONUtils.toJsonString(baseResult));
    }


    @Test
    public void queryRule() {
        QueryB2BRuleRequestDTO dto = new QueryB2BRuleRequestDTO();
        dto.setFlightNo("MU727");
        dto.setCabin("T");
        dto.setTicketPar("3910");
        dto.setPayPrice("5129");
        dto.setTax("1219");
        dto.setCurrencyCode("HKD");

        dto.setDeptDate("2024-05-29");
        dto.setDeptAirport("PVG");
        dto.setArrAirport("NRT");
        dto.setAirCode("MU");
        dto.setAdultNum("1");
        dto.setChildNum("0");
        dto.setId("1");
        dto.setCabinType("Y");



        B2bBaseResult<QueryB2BRuleResponseDTO> baseResult= flightFacade.queryB2BFlightRule(dto);
        System.out.println(JSONUtils.toJsonString(baseResult));
    }

    @Test
    public void tes31t31(){

        B2BCreateOrderRequestDTO b2BCreateOrderRequestDTO = new B2BCreateOrderRequestDTO();
        b2BCreateOrderRequestDTO.setId("1");
        b2BCreateOrderRequestDTO.setDeptTime("2024-05-29 07:55");
        b2BCreateOrderRequestDTO.setAdultNum("1");
        b2BCreateOrderRequestDTO.setChildNum("0");
        b2BCreateOrderRequestDTO.setDeptAirport("PVG");
        b2BCreateOrderRequestDTO.setArrAirport("NRT");
        b2BCreateOrderRequestDTO.setArrTime("2024-05-29 12:00");
        b2BCreateOrderRequestDTO.setAirCode("MU");
        b2BCreateOrderRequestDTO.setCabinType("Y");
        b2BCreateOrderRequestDTO.setFlightNo("MU727");

        b2BCreateOrderRequestDTO.setCabin("T");
        b2BCreateOrderRequestDTO.setTicketPar("1420");
        b2BCreateOrderRequestDTO.setPayPrice("1859");
        b2BCreateOrderRequestDTO.setTax("439");

        b2BCreateOrderRequestDTO.setTicketParAdult("1420");
        b2BCreateOrderRequestDTO.setTaxFeeAdult("439");
        b2BCreateOrderRequestDTO.setPlaneType("321");
        b2BCreateOrderRequestDTO.setCurrencyCode("HKD");
        b2BCreateOrderRequestDTO.setPayCurrencyCode("HKD");

//        B2BAmericanAddressDTO b2BAmericanAddressDTO = new B2BAmericanAddressDTO();
//        b2BAmericanAddressDTO.setCityName("000");
//        b2BAmericanAddressDTO.setCountrySubDivisionName("111");
//        b2BAmericanAddressDTO.setStreetText("222");
//        b2BAmericanAddressDTO.setPostalCode("333");
//        b2BCreateOrderRequestDTO.setAmericanAddressDTO(b2BAmericanAddressDTO);


        List<B2BPassengerDTO> b2BPassengerDTOS = new ArrayList<>();
        B2BPassengerDTO b2BPassengerDTO = new B2BPassengerDTO();
        b2BPassengerDTO.setFirstName("Li");
        b2BPassengerDTO.setLastName("Ming");
        b2BPassengerDTO.setType("ADULT");
        b2BPassengerDTO.setCertType("PP");
        b2BPassengerDTO.setCountry("CN");
        b2BPassengerDTO.setTel("16623152210");
        b2BPassengerDTO.setCertNo("E74835722");
        b2BPassengerDTO.setBirthday("1999-01-01");
        b2BPassengerDTO.setSex("M");
        b2BPassengerDTO.setIssuingCountryCode("CN");
        b2BPassengerDTO.setExpiryDate("2025-03-01");


        b2BPassengerDTOS.add(b2BPassengerDTO);
        b2BCreateOrderRequestDTO.setB2BPassengerParamList(b2BPassengerDTOS);

        B2BContactDTO b2BContactDTO = new B2BContactDTO();
        b2BContactDTO.setContactMobile("16623152210");
        b2BContactDTO.setContactName("Li.CN");
        b2BContactDTO.setContactEmail("<EMAIL>");
        b2BContactDTO.setContactCountryCode("86");

        b2BCreateOrderRequestDTO.setB2BContactParam(b2BContactDTO);

//        b2BCreateOrderRequestDTO = JSONUtils.jsonToBean("",B2BCreateOrderRequestDTO.class);

        orderFacade.createOrder(b2BCreateOrderRequestDTO);
    }


    @Test
    public void tes31t3131(){

        B2BOrderPayRequestDTO b2BOrderPayRequestDTO = new B2BOrderPayRequestDTO();
        b2BOrderPayRequestDTO.setOrderId("2404291622050000002");
        b2BOrderPayRequestDTO.setUserName("120181330");
        b2BOrderPayRequestDTO.setPwd("120181330");
        b2BOrderPayRequestDTO.setId("1");


        orderFacade.orderPay(b2BOrderPayRequestDTO);
    }


    @Test
    public void testRefundTicket(){

        B2bRefundOrderRequestDTO b2BOrderPayRequestDTO = new B2bRefundOrderRequestDTO();
        b2BOrderPayRequestDTO.setOrderId("2404241549280000004");
        b2BOrderPayRequestDTO.setUserId("2404111604000000003");
        b2BOrderPayRequestDTO.setRefundType(RefundTypeEnums.USER.name());

        b2BRefundFacade.refundOrder(b2BOrderPayRequestDTO);
    }

    @Test
    public void testQueryDetail(){

        B2BOrderQueryDetailRequestDTO b2BOrderPayRequestDTO = new B2BOrderQueryDetailRequestDTO();
        b2BOrderPayRequestDTO.setOrderId("2404181059290000004");
        b2BOrderPayRequestDTO.setId("1");


        orderQueryFacade.b2bQueryOrderDetail(b2BOrderPayRequestDTO);
    }



    @Test
    public void testTimerQuery() {
        b2bTicketQueryTimerFacade.timer(13000,0,"REFUND_TICKET","REFUND_ACCPET_SUCCESS");
    }

    @Test
    public void tesQueryList(){

        B2BOrderQueryListRequestDTO b2BOrderPayRequestDTO = new B2BOrderQueryListRequestDTO();
        b2BOrderPayRequestDTO.setId("1");
        b2BOrderPayRequestDTO.setPageSize(20);
        b2BOrderPayRequestDTO.setPageNo(1);
//        b2BOrderPayRequestDTO.setOrderId("2404091550070000002");
        try {
            b2BOrderPayRequestDTO.setCreateDate(DateUtils.sdfDateTime.parse("2024-01-10 00:01:00"));
            b2BOrderPayRequestDTO.setEndDate(DateUtils.sdfDateTime.parse("2024-05-16 00:00:00"));
        }catch (Exception e){

            System.out.println();
        }


        orderQueryFacade.b2bQueryOrderList(b2BOrderPayRequestDTO);
    }



    @Test
    public void test1(){
        QueryFlightInfoDTO dto = new QueryFlightInfoDTO();
        dto.setDeptDate("2023-02-01");
//        dto.setDeptCityCode("110000");
//        dto.setArrCityCode("310000");
        dto.setDeptAirport("PEK");
        dto.setArrAirport("PVG");
        dto.setAdultNum("1");
        dto.setChildNum("0");
        dto.setAirCode("B2CYPMUNDC");
        List<FlightInfoResponseDTO> list = flightBiz.queryFlight(dto);
        System.out.println(JSONUtils.toJsonString(list));
    }
}

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OrderDetailTable>
    <RsStatus>
        <Desc>查询成功！</Desc>
        <Text>SUCCESS</Text>
        <Value>1</Value>
    </RsStatus>
    <detailRefunds/>
    <detailRefunds>
        <detailRefund>
            <applyDate>2021-11-11 17:06:42</applyDate>
            <id>7e231a59d9fc885c330240e27e467828</id>
            <refundAmount>459.0</refundAmount>
            <refundDate>2021-11-11 17:16:25</refundDate>
            <refundNo>R20211111700016</refundNo>
            <refundNum>2021111122001401951430501366</refundNum>
            <refundReson>自愿退票退款</refundReson>
            <refundStatus>已退款</refundStatus>
        </detailRefund>
        <detailRefund>
            <applyDate>2021-11-11 17:17:53</applyDate>
            <id></id>
            <refundAmount>171.0</refundAmount>
            <refundNo>T20211111700001</refundNo>
            <refundNum>2021111122001401951430501366</refundNum>
            <refundReson>非自愿</refundReson>
            <refundStatus>已退款</refundStatus>
        </detailRefund>
    </detailRefunds>
    <basicOrderInformations>
        <basicOrderInformation>
            <adultNumber>1</adultNumber>
            <carrier>GY</carrier>
            <childNumber>0</childNumber>
            <countryCode>D</countryCode>
            <createTime>2021-11-11 17:03:20</createTime>
            <ffeeCharge>-</ffeeCharge>
            <groupType>散客</groupType>
            <infantNum>0</infantNum>
            <orderNo>B20211111700208</orderNo>
            <orderStatus>26</orderStatus>
            <orderStatusCn>已退款</orderStatusCn>
            <peopleNumber>1</peopleNumber>
            <pnr>PTSDBX</pnr>
            <refundOrderNo>R20211111700016</refundOrderNo>
            <salesChannels>测试123</salesChannels>
            <salesCity>PEK</salesCity>
            <stroke>KWE-HAK
            </stroke>
        </basicOrderInformation>
    </basicOrderInformations>
    <changeFreightInformations>
        <changeFreightInformation>
            <cn>50.0</cn>
            <cabin>null-&gt;Y</cabin>
            <change>0.0</change>
            <changeRate>0</changeRate>
            <commission>0.0</commission>
            <fareLevel>Z</fareLevel>
            <name>测试</name>
            <segment>KWE-HAK</segment>
            <seq>1</seq>
            <ticketPrice>570.0</ticketPrice>
            <travellerType>成人</travellerType>
            <yq>10.0</yq>
        </changeFreightInformation>
    </changeFreightInformations>
    <contactInformations>
        <contactInformation>
            <contactNumber>18388255125</contactNumber>
            <contactPerson>测试</contactPerson>
            <currentTimeLimit>2021-11-11 17:32:00</currentTimeLimit>
        </contactInformation>
    </contactInformations>
    <freightInformations>
        <freightInformation>
            <agencyFees>0.0</agencyFees>
            <fare>570.0</fare>
            <fullPrice>1400.0</fullPrice>
            <insurance>0.0</insurance>
            <peopleNumber>1</peopleNumber>
            <subtotal>630.0</subtotal>
            <taxes>60.0</taxes>
            <type>成人</type>
        </freightInformation>
        <freightInformation>
            <agencyFees>0.0</agencyFees>
            <fare>0.0</fare>
            <fullPrice>1400.0</fullPrice>
            <insurance>0</insurance>
            <peopleNumber>0</peopleNumber>
            <subtotal>0.0</subtotal>
            <taxes>0.0</taxes>
            <type>儿童</type>
        </freightInformation>
        <freightInformation>
            <agencyFees>0.0</agencyFees>
            <fare>0.0</fare>
            <fullPrice>1400.0</fullPrice>
            <insurance>0.0</insurance>
            <peopleNumber>0</peopleNumber>
            <subtotal>0.0</subtotal>
            <taxes>0.0</taxes>
            <type>婴儿</type>
        </freightInformation>
    </freightInformations>
    <itineraryInformations>
        <itineraryInformation>
            <adultPrice>570.0</adultPrice>
            <adultTaxes>50.0</adultTaxes>
            <arrChineseName>海口</arrChineseName>
            <arrCode>HAK</arrCode>
            <arrStn>海口美兰国际机场-HAK</arrStn>
            <cabin>Z</cabin>
            <cabinSeatDetail>
                <area>D</area>
                <bagAmount>20</bagAmount>
                <bagMeasureUnit>KG</bagMeasureUnit>
                <bagName>行李总额20KG</bagName>
                <bagRemark>1.随身+托运行李总额总计20KG。 \n2.随身行李限1件，总量不超过5公斤，尺寸不超过20x40x55厘米。 \n3.托运行李每件尺寸不超过40x60x100厘米。
                    \n4.超尺寸和超重的行李均需额外付逾重行李费。 \n5.逾重费率是以每公斤按旅行当日所乘航段经济舱公布票价的1.5%计算，以人民币元为单位，尾数四舍五入。 \n6.婴儿票无免费行李额。
                </bagRemark>
                <cabinSeatLevel>3</cabinSeatLevel>
                <changeRule>
                    [{"appCode":"ADE","endConstain":"N","percent":"10","startConstain":"GT","startH":168},{"appCode":"ADE","endConstain":"LE","endH":168,"percent":"20","startConstain":"GT","startH":72},{"appCode":"ADE","endConstain":"LE","endH":72,"percent":"30","startConstain":"GT","startH":4},{"appCode":"ADE","endConstain":"LE","endH":4,"percent":"60","startConstain":"GE","startH":0},{"appCode":"PDE","endConstain":"N","percent":"60","startConstain":"GT","startH":0}]
                </changeRule>
                <changeRuleInfos>
                    <changeRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>N</endConstain>
                        <percent>10</percent>
                        <startConstain>GT</startConstain>
                        <startH>168</startH>
                    </changeRuleInfo>
                    <changeRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>168</endH>
                        <percent>20</percent>
                        <startConstain>GT</startConstain>
                        <startH>72</startH>
                    </changeRuleInfo>
                    <changeRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>72</endH>
                        <percent>30</percent>
                        <startConstain>GT</startConstain>
                        <startH>4</startH>
                    </changeRuleInfo>
                    <changeRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>4</endH>
                        <percent>60</percent>
                        <startConstain>GE</startConstain>
                        <startH>0</startH>
                    </changeRuleInfo>
                    <changeRuleInfo>
                        <appCode>PDE</appCode>
                        <endConstain>N</endConstain>
                        <percent>60</percent>
                        <startConstain>GT</startConstain>
                        <startH>0</startH>
                    </changeRuleInfo>
                </changeRuleInfos>
                <chgCondCheck>允许变更</chgCondCheck>
                <chgCondCheckInt>1</chgCondCheckInt>
                <discount>0.0</discount>
                <documentName>20210403</documentName>
                <fareLevel>Z</fareLevel>
                <levelrelated>1</levelrelated>
                <name>E/V/Z</name>
                <refundRule>
                    [{"appCode":"ADE","endConstain":"N","percent":"30","startConstain":"GT","startH":168},{"appCode":"ADE","endConstain":"LE","endH":168,"percent":"50","startConstain":"GT","startH":72},{"appCode":"ADE","endConstain":"LE","endH":72,"percent":"60","startConstain":"GT","startH":4},{"appCode":"ADE","endConstain":"LE","endH":4,"percent":"90","startConstain":"GE","startH":0},{"appCode":"PDE","endConstain":"N","percent":"90","startConstain":"GT","startH":0}]
                </refundRule>
                <refundRuleInfos>
                    <refundRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>N</endConstain>
                        <percent>30</percent>
                        <startConstain>GT</startConstain>
                        <startH>168</startH>
                    </refundRuleInfo>
                    <refundRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>168</endH>
                        <percent>50</percent>
                        <startConstain>GT</startConstain>
                        <startH>72</startH>
                    </refundRuleInfo>
                    <refundRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>72</endH>
                        <percent>60</percent>
                        <startConstain>GT</startConstain>
                        <startH>4</startH>
                    </refundRuleInfo>
                    <refundRuleInfo>
                        <appCode>ADE</appCode>
                        <endConstain>LE</endConstain>
                        <endH>4</endH>
                        <percent>90</percent>
                        <startConstain>GE</startConstain>
                        <startH>0</startH>
                    </refundRuleInfo>
                    <refundRuleInfo>
                        <appCode>PDE</appCode>
                        <endConstain>N</endConstain>
                        <percent>90</percent>
                        <startConstain>GT</startConstain>
                        <startH>0</startH>
                    </refundRuleInfo>
                </refundRuleInfos>
                <rescheduleCheck>1</rescheduleCheck>
                <rfdCondCheckInt>1</rfdCondCheckInt>
                <seatCode>Z</seatCode>
                <srCabinSeatDetailRemark>1.允许变更。在航班规定离站时间前168小时（不含）之前，每次变更的话收取对应仓位公布运价的
                    10%变更费;在航班规定离站时间前72小时（不含）至航班规定离站时间前 168小时（含）之间，每次变更的话收取对应仓位公布运价的 20%变更费;在航班规定离站时间前4小时（不含）至航班规定离站时间前
                    72小时（含）之间，每次变更的话收取对应仓位公布运价的 30%变更费;在航班规定离站时间前0小时（含）至航班规定离站时间前 4小时（含）之间，每次变更的话收取对应仓位公布运价的
                    60%变更费;在航班规定离站时间前0小时（不含）之前，每次变更的话收取对应仓位公布运价的 60%变更费;
                    2.允许退票。在航班规定离站时间前168小时（不含）之前提出退票并取消座位，按对应的舱位公布运价收取 30%的退票费;在航班规定离站时间前72小时（不含）至航班规定离站时间前
                    168小时（含）之间，提出退票并取消座位，按对应的舱位公布运价收取 50%的退票费;在航班规定离站时间前4小时（不含）至航班规定离站时间前
                    72小时（含）之间，提出退票并取消座位，按对应的舱位公布运价收取 60%的退票费;在航班规定离站时间前0小时（含）至航班规定离站时间前 4小时（含）之间，提出退票并取消座位，按对应的舱位公布运价收取
                    90%的退票费;在航班规定离站时间前0小时（不含）之前提出退票并取消座位，按对应的舱位公布运价收取 90%的退票费;
                    3.不允许自愿签转。
                </srCabinSeatDetailRemark>
                <transferCondCheck>不允许签转</transferCondCheck>
                <transferCondCheckInt>0</transferCondCheckInt>
                <voluntary>1,2</voluntary>
            </cabinSeatDetail>
            <commission>0.0</commission>
            <depChineseName>贵阳</depChineseName>
            <depCode>KWE</depCode>
            <depStn>贵阳龙洞堡国际机场-KWE</depStn>
            <depTime>2021-11-30 07:30</depTime>
            <flightDate>2021-11-30</flightDate>
            <flightNo>GY7155</flightNo>
            <model>19B</model>
            <publishPrice>570.0</publishPrice>
            <serialNumber>1</serialNumber>
        </itineraryInformation>
    </itineraryInformations>
    <orderAmounts>
        <orderAmount>
            <addedServiceFee>0</addedServiceFee>
            <agencyFees>0.0</agencyFees>
            <completePaymentTime>2021-11-11 17:04:30</completePaymentTime>
            <depositAmount>0</depositAmount>
            <depositRatio>0%</depositRatio>
            <payNumber>1</payNumber>
            <totalPrice>570.0</totalPrice>
            <totalTax>60.0</totalTax>
        </orderAmount>
    </orderAmounts>
    <orderChangeInformations/>
    <orderLogs>
        <orderLog>
            <desc>创建订单, 订单号为：B20211111700208</desc>
            <operateTime>2021-11-11 17:03:20</operateTime>
            <operateType>订单创建</operateType>
            <operater>lx_hu</operater>
        </orderLog>
    </orderLogs>
    <passengerInformations>
        <passengerInformation>
            <birthDate>1991-11-01</birthDate>
            <idNumber>GY202111110101</idNumber>
            <idType>护照</idType>
            <itemStatus>0</itemStatus>
            <name>测试</name>
            <operateTime>1</operateTime>
            <paxId>b0890a515330ee6ac1516b557cf70c01-P-01</paxId>
            <ticketNo>*************</ticketNo>
            <travellerStatus>1</travellerStatus>
            <travellerType>成人</travellerType>
            <travellerTypeInt>0</travellerTypeInt>
        </passengerInformation>
    </passengerInformations>
    <paymentRecords>
        <paymentRecord>
            <bankOrderNumber>B20211111700208ALIPAY</bankOrderNumber>
            <businessType>票款</businessType>
            <transactionAmount>630.0</transactionAmount>
            <transactionMeans>支付宝</transactionMeans>
            <transactionSerialNumber>2021111122001401951430501366</transactionSerialNumber>
            <transactionStatus>成功</transactionStatus>
            <transactionTime>2021-11-11 17:04:30</transactionTime>
            <transactionType>支付</transactionType>
        </paymentRecord>
    </paymentRecords>
</OrderDetailTable>
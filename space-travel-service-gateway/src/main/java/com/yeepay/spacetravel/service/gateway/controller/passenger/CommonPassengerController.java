package com.yeepay.spacetravel.service.gateway.controller.passenger;

import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.service.facade.dto.passenger.request.CommonPassengerRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.passenger.response.CommonPassengerResponseDTO;
import com.yeepay.spacetravel.service.facade.facade.passenger.CommonPassengerFacade;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.gateway.base.BaseController;
import com.yeepay.spacetravel.service.gateway.vo.passenger.AddCommonPassengerParam;
import com.yeepay.spacetravel.service.gateway.vo.passenger.CommonPassengerParam;
import com.yeepay.spacetravel.service.gateway.vo.passenger.CommonPassengerResponseVo;
import com.yeepay.spacetravel.service.gateway.vo.passenger.UpdateCommonPassengerParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17 上午10:40
 */
@RestController
@RequestMapping("/passenger")
@Api(tags = {"常用联系人"})
public class CommonPassengerController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(CommonPassengerController.class);


    @RequestMapping(value = "/queryAllCommonPassenger", method = RequestMethod.POST)
    @ApiOperation(value = "查询全量常用乘机人", notes = "查询全量常用乘机人")
    public BaseResult<List<CommonPassengerResponseVo>> queryAllCommonPassenger(HttpServletResponse request) {
        logger.info("开始查询全量常用乘机人信息");
        try {

            String memberId = getMemberId();
            logger.info("获取会员ID成功：{}", memberId);

            CommonPassengerRequestDTO commonPassengerRequestDTO = new CommonPassengerRequestDTO();
            commonPassengerRequestDTO.setMemberId(memberId);

            CommonPassengerFacade facade = RemoteServiceFactory.getService(CommonPassengerFacade.class);
            BaseResult<List<CommonPassengerResponseDTO>> baseResult = facade.queryAllCommonPassenger(commonPassengerRequestDTO);
            if (!baseResult.isSuccess()) {
                logger.info("查询全量常用乘机人信息未成功");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }

            List<CommonPassengerResponseVo> vos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(baseResult.getData())) {
                List<CommonPassengerResponseDTO> list = baseResult.getData();
                BeanUtils.copyListProperties(list, vos, CommonPassengerResponseVo.class);
            }
            return BaseResult.success(vos);
        } catch (Exception e) {
            logger.error("调用后端接口查询全量常用乘机人信息出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }
    }

    @RequestMapping(value = "/queryCommonPassenger", method = RequestMethod.POST)
    @ApiOperation(value = "查询常用乘机人", notes = "查询常用乘机人")
    public BaseResult<CommonPassengerResponseVo> queryCommonPassenger(HttpServletResponse request, @RequestBody CommonPassengerParam param) {
        logger.info("开始查询常用乘机人信息：{}", JSONUtils.toJsonString(param));
        try {

            String memberId = getMemberId();
            logger.info("获取会员ID成功：{}", memberId);

            CommonPassengerRequestDTO commonPassengerRequestDTO = new CommonPassengerRequestDTO();
            commonPassengerRequestDTO.setMemberId(memberId);
            commonPassengerRequestDTO.setId(param.getId());

            CommonPassengerFacade facade = RemoteServiceFactory.getService(CommonPassengerFacade.class);
            BaseResult<CommonPassengerResponseDTO> baseResult = facade.queryCommonPassenger(commonPassengerRequestDTO);
            if (!baseResult.isSuccess()) {
                logger.info("查询常用乘机人信息未成功");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }

            CommonPassengerResponseVo commonPassengerResponseVo = new CommonPassengerResponseVo();
            if (null != baseResult.getData()) {
                CommonPassengerResponseDTO commonPassengerResponseDTO = baseResult.getData();
                BeanUtils.copyProperties(commonPassengerResponseDTO, commonPassengerResponseVo);
            }
            return BaseResult.success(commonPassengerResponseVo);
        } catch (Exception e) {
            logger.error("调用后端接口查询常用乘机人信息出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }
    }


    @RequestMapping(value = "/updateCommonPassenger", method = RequestMethod.POST)
    @ApiOperation(value = "修改常用乘机人", notes = "修改常用乘机人")
    public BaseResult updateCommonPassenger(HttpServletResponse request, @RequestBody UpdateCommonPassengerParam param) {
        logger.info("修改常用乘机人：{}", JSONUtils.toJsonString(param));
        try {

            String memberId = getMemberId();
            logger.info("获取会员ID成功：{}", memberId);

            CommonPassengerRequestDTO commonPassengerRequestDTO = new CommonPassengerRequestDTO();
            commonPassengerRequestDTO.setFirstName(param.getFirstName());
            commonPassengerRequestDTO.setLastName(param.getLastName());
            commonPassengerRequestDTO.setPersonType(param.getPersonType());
            commonPassengerRequestDTO.setCertType(param.getCertType());
            commonPassengerRequestDTO.setCertNum(param.getCertNum());
            commonPassengerRequestDTO.setPhone(param.getPhone());
            commonPassengerRequestDTO.setMemberId(memberId);
            commonPassengerRequestDTO.setId(param.getId());

            CommonPassengerFacade facade = RemoteServiceFactory.getService(CommonPassengerFacade.class);
            BaseResult baseResult = facade.updateCommonPassenger(commonPassengerRequestDTO);
            if (!baseResult.isSuccess()) {
                logger.info("修改常用乘机人未成功");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }

            return BaseResult.success();
        } catch (Exception e) {
            logger.error("调用后端接口修改常用乘机人信息出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }
    }


    @RequestMapping(value = "/addCommonPassenger", method = RequestMethod.POST)
    @ApiOperation(value = "新增常用乘机人", notes = "新增常用乘机人")
    public BaseResult addCommonPassenger(HttpServletResponse request, @RequestBody AddCommonPassengerParam param) {
        logger.info("新增常用乘机人：{}", JSONUtils.toJsonString(param));
        try {

            String memberId = getMemberId();
            logger.info("获取会员ID成功：{}", memberId);

            CommonPassengerRequestDTO commonPassengerRequestDTO = new CommonPassengerRequestDTO();
            commonPassengerRequestDTO.setFirstName(param.getFirstName());
            commonPassengerRequestDTO.setLastName(param.getLastName());
            commonPassengerRequestDTO.setPersonType(param.getPersonType());
            commonPassengerRequestDTO.setCertType(param.getCertType());
            commonPassengerRequestDTO.setCertNum(param.getCertNum());
            commonPassengerRequestDTO.setPhone(param.getPhone());
            commonPassengerRequestDTO.setMemberId(memberId);

            CommonPassengerFacade facade = RemoteServiceFactory.getService(CommonPassengerFacade.class);
            BaseResult baseResult = facade.addCommonPassenger(commonPassengerRequestDTO);
            if (!baseResult.isSuccess()) {
                logger.info("新增常用乘机人未成功");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }

            return BaseResult.success();
        } catch (Exception e) {
            logger.error("调用后端接口新增常用乘机人信息出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }
    }


    @RequestMapping(value = "/deleteCommonPassenger", method = RequestMethod.POST)
    @ApiOperation(value = "删除常用乘机人", notes = "删除常用乘机人")
    public BaseResult deleteCommonPassenger(HttpServletResponse request, @RequestBody CommonPassengerParam param) {
        logger.info("删除常用乘机人：{}", JSONUtils.toJsonString(param));
        try {

            String memberId = getMemberId();
            logger.info("获取会员ID成功：{}", memberId);

            CommonPassengerRequestDTO commonPassengerRequestDTO = new CommonPassengerRequestDTO();
            commonPassengerRequestDTO.setId(param.getId());
            commonPassengerRequestDTO.setMemberId(memberId);

            CommonPassengerFacade facade = RemoteServiceFactory.getService(CommonPassengerFacade.class);
            BaseResult baseResult = facade.deleteCommonPassenger(commonPassengerRequestDTO);
            if (!baseResult.isSuccess()) {
                logger.info("删除常用乘机人未成功");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }

            return BaseResult.success();
        } catch (Exception e) {
            logger.error("调用后端接口删除常用乘机人信息出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }
    }

}


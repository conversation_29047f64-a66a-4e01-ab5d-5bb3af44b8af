package com.yeepay.spacetravel.service.gateway.controller.file;

import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.service.facade.dto.file.request.QueryAgreementFileRequestDTO;
import com.yeepay.spacetravel.service.facade.dto.file.response.QueryAgreementFileResponseDTO;
import com.yeepay.spacetravel.service.facade.facade.file.AgreementFile;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.gateway.base.BaseController;
import com.yeepay.spacetravel.service.gateway.vo.file.AgreementFileParam;
import com.yeepay.spacetravel.service.gateway.vo.file.AgreementFileVo;
import com.yeepay.yop.sdk.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18 下午4:09
 */
@RestController
@RequestMapping("/file")
@Api(tags = {"协议"})
public class AgreementFileController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(AgreementFileController.class);

    @RequestMapping(value = "/queryAgreementList", method = RequestMethod.POST)
    @ApiOperation(value = "获取协议", notes = "获取协议")
    public BaseResult<List<AgreementFileVo>> queryAgreementList(@RequestBody AgreementFileParam param, HttpServletRequest request) {
        logger.info("获取协议参数{}", JsonUtils.toJsonString(param));
        try {
            QueryAgreementFileRequestDTO dto = new QueryAgreementFileRequestDTO();
            BeanUtils.copyProperties(param, dto);
            AgreementFile facade = RemoteServiceFactory.getService(AgreementFile.class);
            BaseResult<List<QueryAgreementFileResponseDTO>> baseResult = facade.queryAgreementList(dto);
            logger.info("获取协议返回结果为{}", JSONUtils.toJsonString(baseResult));
            if (!baseResult.isSuccess()) {
                logger.info("获取协议未成功返回");
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }
            List<QueryAgreementFileResponseDTO> fileResponseDTOList = baseResult.getData();
            List<AgreementFileVo> data = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fileResponseDTOList)) {

                BeanUtils.copyListProperties(fileResponseDTOList, data, AgreementFileVo.class);
                return BaseResult.success(data);
            } else {
                return BaseResult.fail(ReturnCode.FAIL, "获取协议出现失败");
            }
        } catch (Exception e) {
            logger.error("获取协议出现未知异常", e);
            return BaseResult.fail(ReturnCode.FAIL, e.getMessage());
        }

    }
}

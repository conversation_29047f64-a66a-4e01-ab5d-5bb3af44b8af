package com.yeepay.spacetravel.service.gateway.vo.flight;

import com.yeepay.spacetravel.service.facade.util.FlightCabinClsConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/10 14:35
 * @Description:
 */
@Getter
@Setter
public class FlightCabinClsInfoVo {
    /**
     *成人价格
     */
    @ApiModelProperty(value = "成人价格")
    private BigDecimal priceAdult;
    /**
     *舱位
     */
    @ApiModelProperty(value = "舱位")
    private String cabinCls;

    /**
     * 舱位名称
     */
    @ApiModelProperty(value = "舱位名称")
    @Setter(AccessLevel.NONE)  // 排除自动生成的setter，使用自定义setter
    private String cabinClsName;
    /**
     *餐食Y/N
     */
    @ApiModelProperty(value = "餐食")
    private String meal;
    /**
     *成人机建税
     */
    @ApiModelProperty(value = "成人机建燃油税和")
    private String taxFeeAdult;
    /**
     * 燃油费
     */
    @ApiModelProperty(value = "燃油费")
    private String oilFeeAdult;
    /**
     * 机建费
     */
    @ApiModelProperty(value = "机建费")
    private String amtAdultAirPortFee;
    /**
     *产品码
     */
    @ApiModelProperty(value = "产品码")
    private String productCode;

    @ApiModelProperty(value = "退改政策")
    private String refundAndChangeRuleDes;

    @ApiModelProperty(value = "政策类型")
    private String priceType;

    @ApiModelProperty(value = "成人原价")
    private BigDecimal adultOrigPrice;

    @ApiModelProperty(value = "优惠价格")
    private BigDecimal couponAmount;

    @ApiModelProperty(value = "儿童价格")
    private BigDecimal priceChild;

    @ApiModelProperty(value = "儿童机建燃油税和")
    private BigDecimal taxFeeChild;

    @ApiModelProperty(value = "儿童燃油费")
    private BigDecimal oilFeeChild;

    @ApiModelProperty(value = "儿童机建费")
    private BigDecimal amtChildAirPortFee;

    @ApiModelProperty(value = "政策ID")
    private String cabinPriceId;

    @ApiModelProperty(value = "成人政策ID")
    private String adultCabinPriceItemId;

    @ApiModelProperty(value = "儿童政策ID")
    private String childCabinPriceItemId;

    @ApiModelProperty(value = "报价详情ID")
    private String cabinPriceItemId;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "产品说明")
    private String productDesc;

    /**
     * 自定义setter方法，确保舱位名称正确转换为中文
     * @param cabinClsName 舱位名称或代码
     */
    public void setCabinClsName(String cabinClsName) {
        // 如果传入的是单个字母（舱位代码），则进行转换
        if (cabinClsName != null && cabinClsName.length() == 1 && cabinClsName.matches("[A-Z]")) {
            // 使用FlightCabinClsConvert进行转换
            this.cabinClsName = FlightCabinClsConvert.getCabinName("DEFAULT", cabinClsName);
        } else {
            // 否则直接设置
            this.cabinClsName = cabinClsName;
        }
    }

}

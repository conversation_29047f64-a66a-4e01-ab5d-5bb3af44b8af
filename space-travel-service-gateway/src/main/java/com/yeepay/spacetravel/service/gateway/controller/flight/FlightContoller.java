package com.yeepay.spacetravel.service.gateway.controller.flight;

import com.caucho.hessian.client.HessianProxyFactory;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryFlightInfoDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightCabinClsInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.response.FlightInfoResponseDTO;
import com.yeepay.spacetravel.service.facade.facade.flight.FlightFacade;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.gateway.base.BaseController;
import com.yeepay.spacetravel.service.gateway.interceptor.IgnoreUserToken;
import com.yeepay.spacetravel.service.gateway.vo.flight.FlightBaseInfoVo;
import com.yeepay.spacetravel.service.gateway.vo.flight.FlightCabinClsInfoVo;
import com.yeepay.spacetravel.service.gateway.vo.flight.FlightInfoVo;
import com.yeepay.spacetravel.service.gateway.vo.flight.QueryFlightParam;
import com.yeepay.spacetravel.service.gateway.vo.flight.QueryDepartureFlightParam;
import com.yeepay.spacetravel.service.gateway.vo.flight.QueryReturnFlightParam;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryDepartureFlightDTO;
import com.yeepay.spacetravel.service.facade.dto.flight.request.QueryReturnFlightDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;


/**
 * @author: qingwang.gu
 * @Date: 2022/11/09 18:43
 * @Description:
 */
@RestController
@RequestMapping("/flight")
@Api(tags = {"航班"})
public class FlightContoller extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(FlightContoller.class);

    private FlightFacade getFlightFacade(){
        return RemoteServiceFactory.getService(FlightFacade.class);
       /* try {
            HessianProxyFactory factory = new HessianProxyFactory();
            return (FlightFacade) factory.create(FlightFacade.class, "http://localhost:8081/spacetravel-service-hessian" + "/"+FlightFacade.class.getSimpleName());
        } catch (Exception e) {
            return null;
        }*/

    }


    @RequestMapping(value = "/queryFlight", method = RequestMethod.POST)
    @ApiOperation(value = "查询航班", notes = "查询城市和机场")
    @IgnoreUserToken
    public BaseResult<List<com.yeepay.spacetravel.service.gateway.vo.flight.FlightInfoVo>> queryFlight(HttpServletResponse request, @RequestBody QueryFlightParam param){
        log.info("请求查询航班的参数为{}", JSONUtils.toJsonString(param));
        try{
            QueryFlightInfoDTO dto = new QueryFlightInfoDTO();
            BeanUtils.copyProperties(param,dto);
            BaseResult<List<FlightInfoResponseDTO>> baseResult = getFlightFacade().queryFlight(dto);
            log.info("查询航班返回结果为{}",JSONUtils.toJsonString(baseResult));
            if(!baseResult.isSuccess()){
                log.info("查询航班未成功返回");
                return BaseResult.fail(baseResult.getReturnCode(),baseResult.getReturnMessage());
            }
            List<FlightInfoResponseDTO> list = baseResult.getData();
            List<FlightInfoVo> data = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                for (FlightInfoResponseDTO flightInfoResponseDTO : list) {
                    FlightInfoVo vo = new FlightInfoVo();
                    FlightBaseInfoVo baseInfoVo = new FlightBaseInfoVo();
                    BeanUtils.copyProperties(flightInfoResponseDTO.getFlightBaseInfoResponseDTO(),baseInfoVo);

                    List<FlightCabinClsInfoVo> cabinClsInfoVos = new ArrayList<>();
                    List<FlightCabinClsInfoResponseDTO> flightCabinClsInfoResponseDTOList = flightInfoResponseDTO.getFlightCabinClsInfoResponseDTOs();
                    for (FlightCabinClsInfoResponseDTO flightCabinClsInfoResponseDTO : flightCabinClsInfoResponseDTOList) {

                        FlightCabinClsInfoVo flightCabinClsInfoVo = new FlightCabinClsInfoVo();
                        BeanUtils.copyProperties(flightCabinClsInfoResponseDTO, flightCabinClsInfoVo);

                        // 设置前端回程查询需要的cabinPriceItemId字段
                        // 使用成人政策ID作为主要的价格详情ID
                        if (StringUtils.isNotBlank(flightCabinClsInfoResponseDTO.getAdultCabinPriceItemId())) {
                            flightCabinClsInfoVo.setCabinPriceItemId(flightCabinClsInfoResponseDTO.getAdultCabinPriceItemId());
                        }

                        Map<String, String> refundAndChangeRuleDes = flightCabinClsInfoResponseDTO.getRefundAndChangeRuleDes();
                        if (null != refundAndChangeRuleDes) {

                            for (String key : refundAndChangeRuleDes.keySet()){
                                flightCabinClsInfoVo.setRefundAndChangeRuleDes(refundAndChangeRuleDes.get(key));
                                break;
                            }

                        }

                        cabinClsInfoVos.add(flightCabinClsInfoVo);
                    }

                    for(FlightCabinClsInfoVo tmp:cabinClsInfoVos){
                        if(tmp.getCouponAmount() != null
                                &&tmp.getCouponAmount().compareTo(new BigDecimal("0"))>0){
//                            tmp.setPriceAdult(tmp.getPriceAdult().subtract(tmp.getCouponAmount()));
//                            tmp.setCabinClsName(tmp.getCabinClsName()+"(已立减"+tmp.getCouponAmount()+")");
                            // 移除了无意义的 setCabinClsName 调用
                        }
                    }

                    vo.setFlightBaseInfoVo(baseInfoVo);
                    vo.setCabinClses(cabinClsInfoVos);
                    data.add(vo);
                }
                return BaseResult.success(data);
            }else{
                return BaseResult.fail(ReturnCode.FAIL,"未找到对应的航班");
            }
        }catch (Exception e){
            log.error("请求航班查询出现未知异常",e);
            return BaseResult.fail(ReturnCode.FAIL,e.getMessage());
        }
    }

    @PostMapping("/queryDepartureFlight")
    @ApiOperation(value = "查询往返程去程航班", notes = "查询往返程去程航班列表")
    @IgnoreUserToken
    public BaseResult<List<com.yeepay.spacetravel.service.gateway.vo.flight.FlightInfoVo>> queryDepartureFlight(@RequestBody QueryDepartureFlightParam param) {
        log.info("往返程去程航班查询请求参数: {}", JSONUtils.toJsonString(param));
        
        try {
            // 构建请求DTO
            QueryDepartureFlightDTO requestDTO = new QueryDepartureFlightDTO();
            
            // 从journeyList中获取行程信息
            if (param.getJourneyList() != null && !param.getJourneyList().isEmpty()) {
                // 去程信息（第一个行程）
                QueryDepartureFlightParam.Journey outboundJourney = param.getJourneyList().get(0);
                
                // 智能处理机场/城市代码
                String deptCode = outboundJourney.getDeptAirportCode();
                String arrCode = outboundJourney.getArrAirportCode();
                
                log.info("原始去程信息: 出发代码={}, 到达代码={}, 日期={}", deptCode, arrCode, outboundJourney.getDeptDate());
                
                // 注意：前端传的deptAirportCode可能实际是城市代码（如BJS）
                // 直接传给后端，让后端的智能判断逻辑处理
                requestDTO.setDeptAirport(deptCode);
                requestDTO.setArrAirport(arrCode);
                requestDTO.setDeptDate(outboundJourney.getDeptDate());
                
                // 回程信息处理
                if (param.getJourneyList().size() > 1) {
                    // 如果有两个行程，第二个是回程
                    QueryDepartureFlightParam.Journey returnJourney = param.getJourneyList().get(1);
                    requestDTO.setReturnDate(returnJourney.getDeptDate());
                    log.info("解析回程信息: 出发代码={}, 到达代码={}, 日期={}", 
                            returnJourney.getDeptAirportCode(), returnJourney.getArrAirportCode(), returnJourney.getDeptDate());
                } else {
                    // 如果只有一个行程，使用去程日期+1天作为回程日期（兼容旧版本）
                    String deptDate = requestDTO.getDeptDate();
                    if (deptDate != null) {
                        try {
                            java.time.LocalDate localDate = java.time.LocalDate.parse(deptDate);
                            String returnDate = localDate.plusDays(1).toString();
                            requestDTO.setReturnDate(returnDate);
                            log.warn("前端只传递了一个行程，自动计算回程日期: {} -> {}", deptDate, returnDate);
                        } catch (Exception e) {
                            log.warn("解析日期失败，使用默认回程日期: {}", e.getMessage());
                            requestDTO.setReturnDate(deptDate);
                        }
                    }
                }
            } else {
                log.error("往返程查询缺少行程信息");
                return BaseResult.fail("PARAM_ERROR", "缺少行程信息");
            }
            
            // 设置人数信息
            requestDTO.setAdultNum(param.getAdultNum() != null ? param.getAdultNum().toString() : "1");
            requestDTO.setChildNum(param.getChildNum() != null ? param.getChildNum().toString() : "0");
            
            log.info("最终请求参数: 出发代码={}, 到达代码={}, 去程日期={}, 回程日期={}, 成人数={}, 儿童数={}", 
                    requestDTO.getDeptAirport(), requestDTO.getArrAirport(), requestDTO.getDeptDate(), 
                    requestDTO.getReturnDate(), requestDTO.getAdultNum(), requestDTO.getChildNum());
            
            // 调用服务层
            BaseResult<List<FlightInfoResponseDTO>> baseResult = getFlightFacade().queryDepartureFlight(requestDTO);
            log.info("往返程去程航班查询返回结果: success={}, message={}, dataSize={}", 
                    baseResult.isSuccess(), baseResult.getReturnMessage(), 
                    baseResult.getData() != null ? baseResult.getData().size() : 0);
            
            if (!baseResult.isSuccess()) {
                log.warn("往返程去程航班查询未成功返回: {}", baseResult.getReturnMessage());
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }
            
            // 构建返回数据
            List<FlightInfoResponseDTO> list = baseResult.getData();
            List<FlightInfoVo> data = buildFlightInfoVoList(list);
            
            log.info("往返程去程航班查询成功，返回{}条航班信息", data.size());
            return BaseResult.success(data);
            
        } catch (Exception e) {
            log.error("往返程去程航班查询异常", e);
            return BaseResult.fail("FAIL", "查询航班失败: " + e.getMessage());
        }
    }

    @PostMapping("/queryReturnFlight")
    @ApiOperation(value = "查询往返程回程航班", notes = "查询往返程回程航班列表")
    @IgnoreUserToken
    public BaseResult<List<com.yeepay.spacetravel.service.gateway.vo.flight.FlightInfoVo>> queryReturnFlight(@RequestBody QueryReturnFlightParam param) {
        log.info("往返程回程航班查询请求参数: {}", JSONUtils.toJsonString(param));
        
        try {
            // 构建请求DTO
            QueryReturnFlightDTO requestDTO = new QueryReturnFlightDTO();
            // 补充成人和儿童人数参数
            if (param.getAdultNum() != null) {
                requestDTO.setAdultNum(String.valueOf(param.getAdultNum()));
            }
            if (param.getChildNum() != null) {
                requestDTO.setChildNum(String.valueOf(param.getChildNum()));
            }
            
            // 补充新加字段
            requestDTO.setDeptAirport(param.getDeptAirport());
            requestDTO.setArrAirport(param.getArrAirport());
            requestDTO.setReturnDate(param.getReturnDate());
            
            // 构建去程选择信息
            if (param.getOffer() != null && !param.getOffer().isEmpty()) {
                List<QueryReturnFlightDTO.DepartureOffer> departureOffers = new ArrayList<>();
                for (QueryReturnFlightParam.Offer offer : param.getOffer()) {
                    QueryReturnFlightDTO.DepartureOffer departureOffer = new QueryReturnFlightDTO.DepartureOffer();
                    departureOffer.setCabinPriceId(offer.getCabinPriceId());
                    departureOffer.setCabinPriceItemId(offer.getCabinPriceItemId());
                    departureOffers.add(departureOffer);
                }
                requestDTO.setDepartureOffers(departureOffers);
            }
            
            // 调用服务层
            BaseResult<List<FlightInfoResponseDTO>> baseResult = getFlightFacade().queryReturnFlight(requestDTO);
            log.info("往返程回程航班查询返回结果: {}", JSONUtils.toJsonString(baseResult));
            
            if (!baseResult.isSuccess()) {
                log.warn("往返程回程航班查询未成功返回: {}", baseResult.getReturnMessage());
                return BaseResult.fail(baseResult.getReturnCode(), baseResult.getReturnMessage());
            }
            
            // 构建返回数据
            List<FlightInfoResponseDTO> list = baseResult.getData();
            List<FlightInfoVo> data = buildFlightInfoVoList(list);
            
            return BaseResult.success(data);
            
        } catch (Exception e) {
            log.error("往返程回程航班查询异常", e);
            return BaseResult.fail("FAIL", "查询航班失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建航班信息VO列表
     */
    private List<FlightInfoVo> buildFlightInfoVoList(List<FlightInfoResponseDTO> list) {
        List<FlightInfoVo> data = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            for (FlightInfoResponseDTO flightInfoResponseDTO : list) {
                FlightInfoVo vo = new FlightInfoVo();
                FlightBaseInfoVo baseInfoVo = new FlightBaseInfoVo();
                BeanUtils.copyProperties(flightInfoResponseDTO.getFlightBaseInfoResponseDTO(),baseInfoVo);

                List<FlightCabinClsInfoVo> cabinClsInfoVos = new ArrayList<>();
                List<FlightCabinClsInfoResponseDTO> flightCabinClsInfoResponseDTOList = flightInfoResponseDTO.getFlightCabinClsInfoResponseDTOs();
                for (FlightCabinClsInfoResponseDTO flightCabinClsInfoResponseDTO : flightCabinClsInfoResponseDTOList) {

                    FlightCabinClsInfoVo flightCabinClsInfoVo = new FlightCabinClsInfoVo();
                    BeanUtils.copyProperties(flightCabinClsInfoResponseDTO, flightCabinClsInfoVo);

                    // 设置前端回程查询需要的cabinPriceItemId字段
                    // 使用成人政策ID作为主要的价格详情ID
                    if (StringUtils.isNotBlank(flightCabinClsInfoResponseDTO.getAdultCabinPriceItemId())) {
                        flightCabinClsInfoVo.setCabinPriceItemId(flightCabinClsInfoResponseDTO.getAdultCabinPriceItemId());
                    }

                    Map<String, String> refundAndChangeRuleDes = flightCabinClsInfoResponseDTO.getRefundAndChangeRuleDes();
                    if (null != refundAndChangeRuleDes) {

                        for (String key : refundAndChangeRuleDes.keySet()){
                            flightCabinClsInfoVo.setRefundAndChangeRuleDes(refundAndChangeRuleDes.get(key));
                            break;
                        }

                    }

                    cabinClsInfoVos.add(flightCabinClsInfoVo);
                }

                for(FlightCabinClsInfoVo tmp:cabinClsInfoVos){
                    if(tmp.getCouponAmount() != null
                            &&tmp.getCouponAmount().compareTo(new BigDecimal("0"))>0){
//                        tmp.setPriceAdult(tmp.getPriceAdult().subtract(tmp.getCouponAmount()));
//                        tmp.setCabinClsName(tmp.getCabinClsName()+"(已立减"+tmp.getCouponAmount()+")");
                        // 移除了无意义的 setCabinClsName 调用
                    }
                }

                vo.setFlightBaseInfoVo(baseInfoVo);
                vo.setCabinClses(cabinClsInfoVos);
                data.add(vo);
            }
        }
        return data;
    }

}
